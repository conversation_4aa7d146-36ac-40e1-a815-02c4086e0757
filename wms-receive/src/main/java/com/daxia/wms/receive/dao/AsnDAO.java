package com.daxia.wms.receive.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.Config;
import com.daxia.framework.common.util.ListUtil;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.AsnStatus;
import com.daxia.wms.Constants.CrossDockStatus;
import com.daxia.wms.Constants.DoType;
import com.daxia.wms.Keys;
import com.daxia.wms.receive.entity.AsnHeader;
import com.google.common.collect.Maps;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.criterion.CriteriaSpecification;
import org.jboss.seam.annotations.Name;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 入库管理-ASN业务DAO
 */
@Name("com.daxia.wms.receive.asnDAO")
@lombok.extern.slf4j.Slf4j
public class AsnDAO extends HibernateBaseDAO<AsnHeader, Long> {

    private static final long serialVersionUID = -7125021608325874834L;

    /**
     * 根据Asn代码查找AsnHeader
     *
     * @param asnCode
     * @return
     */
    public AsnHeader getAsnHeaderByAsnCode(String asnCode, Long warehouseId){
    	 String hql = "FROM AsnHeader asnHeader WHERE asnHeader.asnNo = :asnNo and asnHeader.warehouseId = :whId";
         Query query = this.createQuery(hql);
         query.setString("asnNo", asnCode);
         query.setLong("whId", warehouseId);
         return (AsnHeader)query.uniqueResult();

    }

    public Boolean hasCoolSku(Long asnId){
   	 String hql = "FROM AsnDetail ad WHERE ad.asnHeaderId = :asnId and ad.warehouseId = :whId"
   	 		+ " and ad.sku.storageCondition in(3,4) ";
        Query query = this.createQuery(hql);
        query.setLong("asnId", asnId);
        query.setLong("whId", ParamUtil.getCurrentWarehouseId());
        return ListUtil.isNotEmpty(query.list());

   }

    /**
     * 查找未审核的Asn
     *
     * @param fromDate
     *            (可为空)
     * @param toDate
     *            (可为空)
     * @return 收货未审核的asn，object[0] asnNo，object[1] 关联单号， object[2] 收货时间，
     *         object[3] 创建时间
     */
    @SuppressWarnings("unchecked")
	public List<Object> findUnVerifiedAsn(Date fromDate, Date toDate, int startIndex, int pageSize, boolean needPaging){
		Query query = getQuery("select o.asnNo, o.asnRefNo1, o.receivedTime, o.createdAt ", fromDate, toDate);
		if (needPaging && pageSize > 0) {
			query.setFirstResult(startIndex);
			query.setMaxResults(pageSize);
		}
		return query.list();
    }

    /**
     * 统计未审核的Asn
     *
     * @param fromDate
     *            (可为空)
     * @param toDate
     *            (可为空)
     * @return 总数
     */
	public Long countUnVerifiedAsn(Date fromDate, Date toDate){
		Query query = getQuery("select count(*) ", fromDate, toDate);
		Number number = (Number)query.uniqueResult();
		return number.longValue();
    }

	private Query getQuery(String sql, Date fromDate, Date toDate){
    	String hql = sql + "from AsnHeader o " +
    			     "where o.asnStatus = :status and o.warehouseId = :warehouseId ";
    	if(fromDate != null){
    		hql += " and o.receivedTime > :fromDate";
    	}
    	if(toDate != null){
    		hql += " and o.receivedTime < :toDate";
    	}
    	hql += " order by o.receivedTime";
		Query query = createQuery(hql);
		query.setString("status", AsnStatus.RECEIVED.getValue());
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		if(fromDate != null){
			query.setTimestamp("fromDate", fromDate);
		}
		if(toDate != null){
			query.setTimestamp("toDate", toDate);
		}
		return query;
    }

    /**
     * 查找未审核的Asn总数
     *
     * @param fromDate
     *            (可为空)
     * @param toDate
     *            (可为空)
     * @return 收货未审核的asn总数，object[0] asn总数，object[1] 商品类总数， object[2] 单品总数
     */
    public Object findUnVerifiedTotal(Date fromDate, Date toDate){
    	String sql = "select count(distinct header.asn_no), count(distinct detail.sku_id), sum(detail.expt_qty) ";
    		   sql += " from doc_asn_header header, doc_asn_detail detail ";
    		   sql += " where header.id = detail.asn_header_id and header.warehouse_id = :warehouseId and detail.warehouse_id = :warehouseId";
    		   sql += "   and header.asn_status = :status and header.is_deleted = 0 and detail.is_deleted = 0";
    	if(fromDate != null){
    		sql += " and header.received_time > :fromDate";
    	}
    	if(toDate != null){
    		sql += " and header.received_time < :toDate";
    	}
		Query query = createSQLQuery(sql);
		query.setLong("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setString("status", AsnStatus.RECEIVED.getValue());
		if(fromDate != null){
			query.setTimestamp("fromDate", fromDate);
		}
		if(toDate != null){
			query.setTimestamp("toDate", toDate);
		}
		return query.uniqueResult();
    }

    /**
     * 验证所有Asn明细是否已初始化
     *
     * @param asnHeaderId
     * @return
     */
    public boolean isAsnDetailAllInitByAsnHeaderId(Long asnHeaderId){
        String hql = "select count(o.id) from AsnHeader o where o.id =:asnHeaderId and o.warehouseId = :warehouseId and " +
        		"not exists(select 1 from AsnDetail ad where ad.asnHeaderId = o.id and ad.lineStatus != :lineStatus and ad.warehouseId = :warehouseId)";
        Query query = createQuery(hql);
        query.setLong("asnHeaderId", asnHeaderId);
        query.setString("lineStatus", AsnStatus.INITIAL.getValue());
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return ((Number)query.uniqueResult()).intValue() > 0 ? true : false ;
    }

    /**
     * 通过asnRefNo1来查询AsnHeader
     *
     * @param asnRefNo1
     */
    public AsnHeader getAsnHeaderByAsnRefNo1(String asnRefNo1) {
        String hql = "FROM AsnHeader asnHeader WHERE asnHeader.asnRefNo1 = :asnRefNo1 and asnHeader.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("asnRefNo1", asnRefNo1);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (AsnHeader)query.uniqueResult();
    }

    /**
     * 通过asnRefNo1来查询AsnHeader
     *
     * @param asnRefNo1
     */
    @SuppressWarnings("unchecked")
    public List<AsnHeader> findAsnHeadersByStrIds(List<Long> ids) {
        String hql = "FROM AsnHeader asnHeader WHERE asnHeader.id in (:ids) and asnHeader.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setParameterList("ids", ids);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return (List<AsnHeader>)query.list();
    }

    /**
     * 通过预约单头ID查找未完成审核的ASN
     * @param reservationHeaderId
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<AsnHeader> findUnverifiedAsnByReservationHeaderId(Long reservationHeaderId){
        List<String> status = new ArrayList<String>();
        status.add(Constants.AsnStatus.INITIAL.getValue());
        status.add(Constants.AsnStatus.PARTRECEIVE.getValue());
        status.add(Constants.AsnStatus.RECEIVED.getValue());
        String hql = "FROM AsnHeader o WHERE o.warehouseId = :warehouseId and " +
        		"EXISTS (SELECT asnHeaderId from ReservationDetail rd WHERE o.id = asnHeaderId and resHeaderId = :reservationHeaderId " +
        		"and rd.warehouseId = :warehouseId) and o.asnStatus in (:status)";
        Query query = this.createQuery(hql);
        query.setLong("reservationHeaderId", reservationHeaderId);
        query.setParameterList("status", status);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return query.list();
    }

    public boolean isRefTtShiped(String asnNo, Long toWhId) {
        String hql = "SELECT do.id FROM TOCrossDockHeader do WHERE do.edi2 = :whId " +
        		"AND do.refNo2 = :asnNo AND do.doType = :doType AND do.status = :status and do.warehouseId = :warehouseId";
        List<?> list = this.createQuery(hql).setParameter("whId", String.valueOf(toWhId)).setParameter("asnNo", asnNo)
            .setParameter("doType", DoType.ALLOT.getValue())
            .setParameter("status", CrossDockStatus.SHIPED.getValue())
            .setParameter("warehouseId",  ParamUtil.getCurrentWarehouseId()).list();
        return !ListUtil.isNullOrEmpty(list);
    }


    /**
     * 通过asnRefNo1来查询AsnHeader
     *
     * @param asnRefNo1
     */
    public boolean existAsnHeaderByAsnRefNo1(String asnRefNo1) {
        String hql = "FROM AsnHeader asnHeader WHERE asnHeader.asnRefNo1 = :asnRefNo1 and asnHeader.warehouseId = :warehouseId";
        Query query = this.createQuery(hql);
        query.setString("asnRefNo1", asnRefNo1);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        return 0 == query.list().size() ? false : true;
    }

	/**
	 * 通过asnRefNo1来查询AsnHeader
	 *
	 * @param asnRefNo1
	 */
	public Long getAsnHeaderIDByAsnRefNo1(String asnRefNo1) {
		String hql = "SELECT asnHeader.id FROM AsnHeader asnHeader WHERE asnHeader.asnRefNo1 = :asnRefNo1 AND "
				+ " asnHeader.warehouseId = :warehouseId ORDER BY asnHeader.createdAt ASC LIMIT 1";
		Query query = this.createQuery(hql);
		query.setString("asnRefNo1", asnRefNo1);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		return (Long) query.uniqueResult();
	}

    /**
	 * 通过asnRefNo1来查询AsnHeader
	 *
	 * @param asnRefNo1
	 * @param asnHeaderId
	 */
	public void deleteAsnHeaderByAsnRefNo(String asnRefNo1, Long asnHeaderId) {
		//删除ASN明细
		String hql = " delete from doc_asn_detail where ASN_HEADER_ID in (select asnheader.ID from doc_asn_header asnheader "
				+ " where (asnheader.IS_DELETED = 0) and asnheader.ASN_REF_NO1 = :asnRefNo1 "
				+ " and asnheader.WAREHOUSE_ID = :warehouseId and asnheader.ID <> :asnHeaderId ) ";
		Query query = this.createSQLQuery(hql);
		query.setString("asnRefNo1", asnRefNo1);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("asnHeaderId", asnHeaderId);
		query.executeUpdate();
		//删除ASN头
		hql = " delete from doc_asn_header where ASN_REF_NO1 = :asnRefNo1 and WAREHOUSE_ID = :warehouseId and ID <> :asnHeaderId ";
		query = this.createSQLQuery(hql);
		query.setString("asnRefNo1", asnRefNo1);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("asnHeaderId", asnHeaderId);
		query.executeUpdate();
	}
	@Deprecated
	public Long getAsnHeaderId(){
		String hql = " select SEQ_ASN_HEADER_ID.Nextval from dual ";
		Query query = this.createSQLQuery(hql);
		return ((BigDecimal) query.uniqueResult()).longValue();
	}
	@Deprecated
	public Long getAsnDetailId(){
		String hql = " select Seq_Asn_Detail_ID.Nextval from dual ";
		Query query = this.createSQLQuery(hql);
		return ((BigDecimal) query.uniqueResult()).longValue();
	}

	public String getAsnRefNo1ByCartonNo(String cartonNo){
		String hql = "  SELECT DDH.DO_NO "
				+ "  FROM doc_carton_header DCH "
				+ "  INNER JOIN doc_do_header DDH "
				+ "     ON DCH.DO_HEADER_ID = DDH.ID "
				+ "       WHERE DCH.CARTON_NO = :cartonNo "
				+ "     AND DCH.WAREHOUSE_ID = :warehouseId "
				+ "     AND DDH.WAREHOUSE_ID = :warehouseId ";
		Query query = this.createSQLQuery(hql);
		query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		query.setParameter("cartonNo",cartonNo);
		return (String) query.uniqueResult();
	}


    /**
     * 根据Asn -userDefine01查找AsnHeader
     * @param userDefine01
     * @return
     */
    public AsnHeader getAsnHeaderByUserDefine(Long  userDefine01 ,Long whId){
         String hql = "FROM AsnHeader asnHeader WHERE asnHeader.userDefine01 = :userDefine01 and asnHeader.warehouseId = :whId";
         Query query = this.createQuery(hql);
         query.setLong("userDefine01",userDefine01);
         query.setLong("whId", whId);
         return (AsnHeader)query.uniqueResult();

    }
    /**
     * 通过asnType来查询Asn
     * @param asnType
     * @param asnStatus
     * @param maxNum
     */
    @SuppressWarnings("unchecked")
	public List<AsnHeader> findAsnByType(String asnType,String asnStatus,int maxNum) {
        String hql = "FROM AsnHeader asnHeader WHERE asnHeader.asnType = :asnType and asnHeader.asnStatus = :asnStatus and asnHeader.warehouseId = :warehouseId order by asnHeader.createdAt desc";
        Query query = this.createQuery(hql);
        query.setParameter("asnType", asnType);
        query.setParameter("asnStatus", asnStatus);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(maxNum);
        return query.list();
    }
	/**
	 * 根据Asn 外部编码来查找ASN 信息
	 *
	 * @param originalId
	 * @return
	 */
	public AsnHeader getAsnHeaderByOriginalId(String originalId, Long warehouseId){
		String hql = "FROM AsnHeader asnHeader WHERE asnHeader.originalId = :originalId and asnHeader.warehouseId = :warehouseId";
		Query query = this.createQuery(hql);
		query.setString("originalId", originalId);
		query.setLong("warehouseId", warehouseId);
		return (AsnHeader)query.uniqueResult();
	}

	public void updatePrintFlag(List<Long> ids, Integer printFlag) {
		String sql = "UPDATE doc_asn_header asnHeader SET asnHeader.print_flag = asnHeader.print_flag | :printFlag WHERE asnHeader.id in (:ids)";
		Query query = this.createSQLQuery(sql);
		query.setParameterList("ids", ids);
		query.setParameter("printFlag", printFlag);
		query.executeUpdate();
	}

    public boolean existPrinted(List<Long> ids, Integer printFlag) {
        String sql = "SELECT count(id) FROM doc_asn_header asnHeader WHERE ((asnHeader.print_flag & :printFlag) != 0) AND asnHeader.id in (:ids)";
        Query query = this.createSQLQuery(sql);
        query.setParameterList("ids", ids);
        query.setParameter("printFlag", printFlag);
        BigInteger count = (BigInteger) query.setMaxResults(1).uniqueResult();

        return count != null && count.intValue() > 0;
    }


	public List<Map<String, Object>> getAutoAsn(Integer autoFlag) {
		Map<String, Object> params = Maps.newHashMap();
		switch (autoFlag.intValue()) {
			case AsnHeader.FLAG_AUTO_RECEIVE:
				params.put("asnStatus", AsnStatus.INITIAL.getValue());
				break;
			case AsnHeader.FLAG_AUTO_RECHECK:
				params.put("asnStatus", AsnStatus.RECEIVED.getValue());
				break;

			case AsnHeader.FLAG_AUTO_RECEIVE_VERIFY:
				params.put("asnStatus", AsnStatus.RECEIVED.getValue());
				break;
			case AsnHeader.FLAG_AUTO_PUTAWAY:
				params.put("asnStatus", AsnStatus.ORDER_CLOSED.getValue());
				break;
		}

		params.put("failedNumberMax", Config.getInt(Keys.Receive.auto_fail_max_count, Config.ConfigLevel.GLOBAL,5));
		params.put("autoFlag", autoFlag);

		String sql = "SELECT ah.warehouse_id AS warehouseId, ah.id AS id FROM doc_asn_header ah " +
				"WHERE ah.asn_Status = :asnStatus AND ah.failed_number < :failedNumberMax AND ah.is_deleted = 0 AND " +
				"(ah.auto_flag & :autoFlag) != 0 " +
				"AND ah.create_time >=  TIMESTAMPADD(DAY, -5, NOW()) ";
		if(autoFlag.intValue() == AsnHeader.FLAG_AUTO_RECHECK){
			sql +=" and  exists (select 1 from doc_receive_recheck_header drrh  where drrh.doc_id = ah.id and drrh.status = '00') ";
		}
		if(autoFlag.intValue() == AsnHeader.FLAG_AUTO_RECEIVE_VERIFY){
			sql +=" and not exists (select 1 from doc_receive_recheck_header drrh  where drrh.doc_id = ah.id and drrh.status != '50') ";
		}
				if(autoFlag.intValue() == AsnHeader.FLAG_AUTO_PUTAWAY){
			sql +=" and exists( select 1 from trs_putaway_task tp where tp.doc_id = ah.id and tp.task_status = '40' " +
					"and tp.is_deleted = 0 ) ";
		}
		SQLQuery sqlQuery = (SQLQuery) this.createSQLQuery(sql, params);
		return sqlQuery.addScalar("warehouseId", Hibernate.LONG).addScalar("id", Hibernate.LONG).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).setMaxResults(10).list();
	}

	public Boolean existsByAsnCode(String asnCode, Long warehouseId) {
		String hql = "select COUNT(id) FROM AsnHeader asnHeader WHERE asnHeader.asnNo = :asnNo and asnHeader.warehouseId = :whId";
		Query query = this.createQuery(hql);
		query.setString("asnNo", asnCode);
		query.setLong("whId", warehouseId);
		return Integer.valueOf(query.uniqueResult().toString()) > 0 ? true : false;

	}

    public List<AsnHeader> findPreReceiveList(List<String> notAllowAsnTypes) {
        String hql = "FROM AsnHeader asnHeader WHERE asnHeader.asnStatus in ('00','10','40') and asnHeader.needCrossStock=0 and ifnull(asnHeader.asnSubType,'')!='CTN'  " +
				"and asnHeader.warehouseId = :warehouseId " ;
		if(!CollectionUtils.isEmpty(notAllowAsnTypes)){
			hql += "and asnHeader.asnType in (:notAllowAsnTypes) ";
		}
		hql += "order by asnHeader.updatedAt desc";
        Query query = this.createQuery(hql);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
		if(!CollectionUtils.isEmpty(notAllowAsnTypes)) {
			query.setParameterList("notAllowAsnTypes", notAllowAsnTypes);
		}
        query.setMaxResults(100);
        return query.list();
    }

    public AsnHeader findAsnHeaderByContainerNo(String containerNo) {
        String hql = " FROM AsnHeader asnHeader WHERE asnHeader.warehouseId = :warehouseId " +
                " and exists (select 1 from ReceiveBatch rb where rb.docId = asnHeader.originalId and rb.lotatt13 = :containerNo )" +
                "order by asnHeader.updatedAt desc ";
        Query query = this.createQuery(hql);
        query.setParameter("containerNo", containerNo);
        query.setParameter("warehouseId", ParamUtil.getCurrentWarehouseId());
        query.setMaxResults(1);
        return (AsnHeader) query.uniqueResult();
    }

    public Integer[] findContainerNos(Long asnId) {
        String sql = "select count(distinct b.LOTATT13) count from doc_batch b,doc_asn_header ah ";
        sql += " where ah.original_id = b.doc_id ";
        sql += " and ah.id = :asnId and ifnull(b.LOTATT13,'')!='' ";
        SQLQuery query = this.createSQLQuery(sql);
        query.setParameter("asnId", asnId);
        query.addScalar("count", Hibernate.INTEGER);
        Integer count1 = (Integer) query.setMaxResults(1).uniqueResult();

        sql = "select count(distinct b.LOTATT13) count from doc_batch b,doc_asn_header ah ";
        sql += " where ah.original_id = b.doc_id ";
        sql += " and ah.id = :asnId and ifnull(b.LOTATT13,'')!='' ";
        sql += " and exists ( select 1 from doc_batch b1 where b1.id  = b.id and b1.received_flag=1 ) ";
        query = this.createSQLQuery(sql);
        query.setParameter("asnId", asnId);
        query.addScalar("count", Hibernate.INTEGER);

        Integer count2 = (Integer) query.setMaxResults(1).uniqueResult();
        return new Integer[]{count1, count2};
    }

    public boolean ctnAllReceived(String asnNo, Long skuId) {
        String sql = "SELECT count(b.id) FROM doc_batch b WHERE b.sku_id = :skuId and b.ref_no = :asnNo and b.is_deleted = 0 and b.received_flag = 0 and ifnull(b.to_local,0) = 0 and b.lotatt14 = 'CTN' ";
        Query query = this.createSQLQuery(sql);
        query.setParameter("skuId", skuId);
        query.setParameter("asnNo", asnNo);
        BigInteger count = (BigInteger) query.setMaxResults(1).uniqueResult();
        return count != null && count.intValue() > 0;
    }
}
