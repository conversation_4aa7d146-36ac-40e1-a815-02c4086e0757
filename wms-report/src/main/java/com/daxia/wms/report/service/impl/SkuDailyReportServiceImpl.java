package com.daxia.wms.report.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.report.dao.SkuDailyReportDAO;
import com.idanchuang.component.base.JsonResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.time.Instant;
import java.util.List;

@Name("SkuDailyReportService")
@Path("/api/skuDailyReport")
@lombok.extern.slf4j.Slf4j
public class SkuDailyReportServiceImpl {

    @In
    private SkuDailyReportDAO skuDailyReportDAO;

    @POST
    @Path("/deleteByDay")
    @Produces("application/json")
    public JsonResult batchDeleteDailyReport(@RequestBody SkuDailyReportDeleteRequest request) {
        List<String> days = request.getDays();
        String barcode = request.getBarcode();
        try {
            ParamUtil.setCurrentTenantCode("ABM");
            for (String day : days) {
                Instant now = Instant.now();
                skuDailyReportDAO.batchDeleteByBarcodes(day, barcode);
                log.info("删除sku日历库存成功.day:{}，耗时:{}", day, Instant.now().toEpochMilli() - now.toEpochMilli());
            }
            return JsonResult.success(days);
        } catch (Exception e) {
            log.error("删除sku日历库存失败.day:{}", days, e);
            return JsonResult.failure(e.getMessage());
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuDailyReportDeleteRequest {
        private List<String> days;
        private String barcode;
    }

}
