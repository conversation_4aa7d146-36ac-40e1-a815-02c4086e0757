package com.daxia.wms.report.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.wms.stock.stock.entity.StockDaily;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Query;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;


/**
 * <p>日历库存</p>
 */
@Name("skuDailyReportDAO")
@lombok.extern.slf4j.Slf4j
public class SkuDailyReportDAO extends HibernateBaseDAO<StockDaily, Long> {

    @Transactional
    public void batchDeleteByBarcodes(String day, String barcode) {
        String sql = "DELETE FROM stk_daily WHERE day_date =:day " +
                "and qty_total=0 and qty_available=0 and qty_available_unit=0 and qty_hold=0 and qty_hold_unit=0 ";
        if (StringUtils.isNotBlank(barcode)) {
            sql += " and ean13 =:barcode";
        }
        Query query = createSQLQuery(sql);
        query.setParameter("day", day);
        if (StringUtils.isNotBlank(barcode)) {
            query.setParameter("barcode", barcode);
        }
        query.executeUpdate();
    }

}
