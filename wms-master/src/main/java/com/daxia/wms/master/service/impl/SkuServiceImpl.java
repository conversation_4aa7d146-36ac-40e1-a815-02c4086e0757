package com.daxia.wms.master.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.daxia.framework.common.util.*;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.integration.EcologyOrderIntegration;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.dao.*;
import com.daxia.wms.master.entity.*;
import com.daxia.wms.master.entity.dto.SkuGradeInfoDTO;
import com.daxia.wms.master.filter.PackageInfoHeaderFilter;
import com.daxia.wms.master.filter.SkuFilter;
import com.daxia.wms.master.service.SkuCache;
import com.daxia.wms.master.service.SkuPickLocAssnService;
import com.daxia.wms.master.service.SkuService;
import com.idanchuang.ims.common.enums.InventoryBatchGoodsGradeEnum;
import jxl.Sheet;
import org.apache.commons.collections.CollectionUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;
import org.jboss.seam.annotations.async.Asynchronous;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.daxia.wms.master.MasterException.QA_SKU_NOT_EXIST;

/**
 * 货主产品信息Service
 */
@Name("com.daxia.wms.master.skuService")
@lombok.extern.slf4j.Slf4j
public class SkuServiceImpl implements SkuService {
    @In
    private SkuDAO skuDAO;
    @In
    private ProductBarcodeDAO productBarcodeDAO;

    @In
    private SkuCache skuCache;

    @In
    private ValidityGoodsRuleDAO validityGoodsRuleDAO;

    @In
    private SkuLocationOperationMapDAO skuLocationOperationMapDAO;

    @In
    private SkuPickLocAssnService skuPickLocAssnService;

    @In
    private CategoryDAO categoryDAO;

    @In
    private PutawayRuleHeaderDAO putawayRuleHeaderDAO;

    @In
    private AllocationRuleDAO allocationRuleDAO;

    @In
    private RotationRuleDAO rotationRuleDAO;

    @In
    private PartitionDAO partitionDAO;

    @In
    private LocationDAO locationDAO;

    @In
    private PackageInfoDetailDAO packageInfoDetailDAO;
    @In
    private PackageInfoHeaderDAO packageInfoHeaderDAO;

    @In(create = true)
    private EcologyOrderIntegration ecologyOrderIntegration;

    /* (non-Javadoc)
     * @see com.daxia.wms.master.service.SkuService#querySku(com.daxia.wms.master.filter.SkuFilter, int, int)
     */
    @Override
    public DataPage<Sku> querySku(SkuFilter skuFilter, int startIndex, int pageSize) {
        return skuDAO.findRangeByFilter(skuFilter, startIndex, pageSize);
    }

    @Override
    @SuppressWarnings("deprecation")
    public List<Sku> querySku(SkuFilter skuFilter) {
        return skuDAO.findByFilter(skuFilter);
    }

    /*
     * (non-Javadoc)
     * @see com.daxia.wms.master.service.SkuService#getSku(java.lang.Long)
     */
    @Override
    public Sku getSkuById(Long skuId) {
        return skuDAO.get(skuId);
    }

    @Override
    @Transactional
    public void saveUpdate(Sku sku) throws MasterException {
        boolean skuVolumeIsChange = false;
        if (sku.getId() != null) {
            skuVolumeIsChange = skuVolumeIsChange(sku);
        }
        if (YesNo.YES.getValue().equals(sku.getDotFlag()) && sku.getPackageId() != null && Config.isDefaultTrue(Keys.Master.dot_flag_package, Config.ConfigLevel.TENANT)) {
            List<PackageInfoDetail> details = packageInfoDetailDAO.findByPackageId(sku.getPackageId());
            if (details != null && details.size() > 1) {
                throw new MasterException(MasterException.PACKAGE_DETAIL_SAME_QTY_EXIST);
            }
        }
        //        validateSku(sku);
        //根据ProductCode进行查询，如果空，则插入保存；否则，走更新。
        Sku skuDO = skuDAO.getSku(sku.getProductCode());
        if (skuDO == null)
            skuDAO.save(sku);
        else
            skuDAO.update(sku);
        skuCache.update(sku.getId());

        if (skuVolumeIsChange) {
            skuPickLocAssnService.updateLimit(sku.getId(), null);
        }
    }

    @Override
    @Transactional
    public void update(Sku sku) throws MasterException {
        boolean skuVolumeIsChange = skuVolumeIsChange(sku);
        if (YesNo.YES.getValue().equals(sku.getDotFlag()) && sku.getPackageId() != null && Config.isDefaultTrue(Keys.Master.dot_flag_package, Config.ConfigLevel.TENANT)) {
            List<PackageInfoDetail> details = packageInfoDetailDAO.findByPackageId(sku.getPackageId());
            if (details != null && details.size() > 1) {
                throw new MasterException(MasterException.PACKAGE_DETAIL_SAME_QTY_EXIST);
            }
        }
        skuDAO.update(sku);
        skuCache.update(sku.getId());

        if (skuVolumeIsChange) {
            skuPickLocAssnService.updateLimit(sku.getId(), null);
        }
    }

    @Override
    public boolean needSerialScan(Sku sku) {
        return YesNo.YES.getValue().equals(sku.getSnQty());
    }

    //
    private boolean skuVolumeIsChange(Sku sku) {
        if (!skuPickLocAssnService.useVolumeCountLimit()) {
            return false;
        }

        Double volume = skuDAO.getVolume(sku.getId());
        return CompareUtil.compare(sku.getVolume(), volume, false) != 0;
    }

    @Override
    public List<Sku> queryBarcodeList(String barcode) {
        List<Sku> skuList = skuDAO.findBarcodeList(barcode);
        if (skuList == null || skuList.size() == 0) {
            return null;
        }
        return skuList;
    }

    @Override
    public Sku getSku(String productCode) {
        return skuDAO.getSku(productCode);
    }

    @Override
    public List<Sku> getSkuByBarcode(String barcode) {
        return skuDAO.getSkuByBarcode(barcode);
    }

    @Override
    public List<Sku> querySkuList(List<Long> ids) {
        return skuDAO.getByKeys(ids);
    }


    @Override
    @Transactional
    public void addLocationOperateRecord(Long skuId, String locType, Long locId, String trsType, Long partitionId) {
        SkuLocationOperationMap map = skuLocationOperationMapDAO.query(skuId, locType, trsType, partitionId);
        if (map == null) {
            map = new SkuLocationOperationMap();
            map.setSkuId(skuId);
            map.setLocType(locType);
            map.setTrsType(trsType);
            map.setPartitionId(partitionId);
            map.setWarehouseId(ParamUtil.getCurrentWarehouseId());
        }
        map.setOperateTime(new Date());
        map.setLocId(locId);
        skuLocationOperationMapDAO.saveOrUpdate(map);
    }

    @Override
    public SkuLocationOperationMap getLastOperateLocation(Long skuId, Long partitionId, String locType[], String... trsTypes) {
        return skuLocationOperationMapDAO.query(skuId, partitionId, locType, trsTypes);
    }

    @Override
    public SkuLocationOperationMap getLastOperateLocations(Long skuId, Long partitionId, String locType[],
                                                           Set<String> cycleClassSet, String priceClass, String... trsTypes) {
        return skuLocationOperationMapDAO.getLastOperateLocations(skuId, partitionId, locType, cycleClassSet, priceClass, trsTypes);
    }

    @Override
    public SkuDAO getSkuDAO() {
        return skuDAO;
    }

    /**
     * 根据商品条码/商品编码找其SkuId，不会返回Null
     *
     * @param code 商品条码/商品编码
     * @return
     */
    @Override
    public List<Long> findSkuIdByCode(String code) {
        List<Long> skuIds = new ArrayList<Long>();
        if (StringUtil.isEmpty(code)) {
            return skuIds;
        }
        List<ProductBarcode> barcodes = productBarcodeDAO.findBarcodeList(code);
        if (!ListUtil.isNullOrEmpty(barcodes)) {
            for (ProductBarcode barcode : barcodes) {
                if (null != barcode.getSkuId() && !skuIds.contains(barcode.getSkuId())) {
                    skuIds.add(barcode.getSkuId());
                }
            }
        }
        if (skuIds.isEmpty()) {
            Sku sku = skuDAO.getSku(code);
            if (null != sku && !skuIds.contains(sku.getId())) {
                skuIds.add(sku.getId());
            }
        }
        return skuIds;
    }

    @Override
    public Sku getSkuByCodeAndBarcode(String code, String barcode) {
        return skuDAO.getSkuByCodeAndBarcode(code, barcode);
    }

    @Override
    public List<Sku> findSkusByProcuctCodes(List<String> codes) {
        return skuDAO.findSkusByCodes(codes);
    }

    @Override
    @Transactional
    public void updateSkuTiHiInfo(Long skuId, Integer hiQty, Integer tiQty) {
        skuDAO.updateSkuTiHiInfo(skuId, hiQty, tiQty);
    }

    @Override
    @Transactional
    public void saveSkuFromSheet(Sheet sheet, Set<String> errorSet) {
        int cr = sheet.getRows();
        if (2 > cr) {
            throw new MasterException(MasterException.EXCEL_DATA_ERROR);
        }
        //记录错误行
        List<String> errRows = new ArrayList<String>();
        for (int i = 1; i < cr; i++) {
            try {
                Map<String, Object> map = validateSheet(sheet, i);
                if (!(Boolean) map.get("flag")) {
                    continue;
                }
                String productCode = (String) map.get("productBarCode");
                List<Sku> skuList = skuDAO.getSkuByEan13(productCode);
                Sku sku = skuList.get(0);

                sku.setGrossweight(getDoubleValueFmObj(map.get("grossWeight")));
                sku.setNeightweight(getDoubleValueFmObj(map.get("netWeight")));
                skuDAO.saveOrUpdate(sku);


            } catch (Exception e) {
                String errMsg = null;
                if (e instanceof BusinessException) {
                    errMsg = "excel 保存第" + (i + 1) + "行数据异常 " +
                            ResourceUtils.getDispalyString(e.getMessage(), e.getClass().getName(),
                                    ((BusinessException) e).getParams());

                } else {
                    errMsg = "excel 保存第" + (i + 1) + "行数据异常  " + e.getMessage();
                }
                errRows.add(errMsg);
                log.error(errMsg, e);
            }
        }
        String msg = null;
        if (ListUtil.isNotEmpty(errRows)) {
            msg = ListUtil.collection2String(errRows, ",");

        } else {
            msg = "添加成功";
        }
        errorSet.add(msg);
    }

    private Map<String, Object> validateSheet(Sheet sheet, int i) {
        Map<String, Object> map = new HashMap<String, Object>();

        String productBarCode = sheet.getCell(0, i).getContents();
        if (StringUtil.isEmpty(productBarCode)) {
            throw new MasterException(MasterException.ERROR_PRODUCTBARCODE_CANNOT_NULL);
        }
        map.put("productBarCode", productBarCode);

        //校验毛重
        String grossWeight = sheet.getCell(1, i).getContents();
        if (!ImportUtils.isNumber(grossWeight)) {
            throw new MasterException(MasterException.EXCEL_DATA_ILLEGAL, "毛重");
        }
        map.put("grossWeight", grossWeight);

        //校验净重
        String netWeight = sheet.getCell(2, i).getContents();
        if (!ImportUtils.isNumber(netWeight)) {
            throw new MasterException(MasterException.EXCEL_DATA_ILLEGAL, "净重");
        }
        map.put("netWeight", netWeight);
        map.put("flag", Boolean.TRUE);
        return map;
    }

    private Double getDoubleValueFmObj(Object obj) {
        if (null == obj) {
            return Double.valueOf("0");
        }
        return Double.valueOf(obj.toString());
    }

    @Override
    public List<Sku> findSkusByName(String name) {
        return skuDAO.findSkusByName(name);
    }

    @Override
    public List<Sku> getSkuByEan13(String barcode) {
        return skuDAO.getSkuByEan13(barcode);
    }

    @Override
    public Sku getSkuByOrigId(String origId) {
        return skuDAO.getSkuByOrigId(origId);
    }

    @Override
    public Sku getSkuByOrigId(String id, Long tenantId) {
        if (StringUtil.isEmpty(id)) {
            return null;
        }
        return this.skuDAO.getByOrigId(id, tenantId);
    }

    @Override
    @Transactional
    public void createDefaultPackageInfo(List<Sku> skus) {
        for (Sku sku : skus) {
            createDefaultPackageInfoBySku(sku);
        }
    }

    @Override
    @Transactional
    public void createDefaultPackageInfoBySku(Sku sku) {
        if (sku == null) {
            return;
        }
        if (sku.getPackageId() == null || sku.getPackageId() < 0) {
            PackageInfoHeaderFilter packageInfoFilter = new PackageInfoHeaderFilter();
            packageInfoFilter.setSkuId(sku.getId());
            List<PackageInfoHeader> headers = packageInfoHeaderDAO.findByFilter(packageInfoFilter);
            if (!CollectionUtils.isEmpty(headers)) {
                for (PackageInfoHeader header : headers) {
                    packageInfoHeaderDAO.deleteById(header.getId());
                }
            }
            PackageInfoHeader header = new PackageInfoHeader();
            header.setSkuId(sku.getId());
            header.setSku(sku);
            header.setPkgCode("PC");
            header.setPkgDesc("个(1)");
            header.setTenantId(sku.getTenantId());
            packageInfoHeaderDAO.save(header);
            PackageInfoDetail detail = new PackageInfoDetail();
            detail.setPackageInfoHeader(header);
            detail.setPackageId(header.getId());
            detail.setQty(sku.getUdf4() != null ? new BigDecimal(sku.getUdf4()) : BigDecimal.ONE);
            detail.setPackUom("个");
            detail.setUomDescr("个(1)");
            detail.setTenantId(sku.getTenantId());
            Boolean autoSetMainFlg = Config.isDefaultTrue(Keys.Interface.base_skuAutoSetMainFlg, Config.ConfigLevel.TENANT);
            if (autoSetMainFlg) {
                detail.setMainFlag("1");
            }
            packageInfoDetailDAO.save(detail);
            sku.setPackageId(header.getId());
            if (sku.getStockUnit() == null || sku.getStockUnit() < 0) {
                sku.setStockUnit(detail.getId());
            }
            if (sku.getShippmentUnit() == null || sku.getShippmentUnit() < 0) {
                sku.setShippmentUnit(detail.getId());
            }
            skuDAO.update(sku);
            skuCache.update(sku.getId());
        }
    }

    @Override
    public Map<String, Long> findOriginIdMap(List<String> orginIds, Long tenantId) {
        Map<String, Long> map = new HashMap<String, Long>();
        List<Sku> skus = this.skuDAO.getSkuByOrigIds(orginIds, tenantId);
        for (Sku sku : skus) {
            map.put(sku.getOriginalId(), sku.getId());
        }
        return map;
    }

    @Override
    public List<String> findByLocationId(Long locationId) {
        SkuFilter skuFilter = new SkuFilter();
        skuFilter.setDefaultLocationId(locationId);
        List<Sku> skuList = skuDAO.findByFilter(skuFilter);
        if (CollectionUtils.isNotEmpty(skuList)) {
            List<String> productCodes = new ArrayList<String>();
            for (Sku sku : skuList) {
                productCodes.add(sku.getProductCode());
            }
            return productCodes;
        }
        return null;
    }

    @Override
    @Transactional
    @Asynchronous
    public void updateSkuLocationOperationByPickList(List<Long> idList, Long warehouseId) {
        ParamUtil.setCurrentWarehouseId(warehouseId);
        skuLocationOperationMapDAO.updateSkuLocationOperationByPickList(idList);
    }

    @Override
    public Map<String, Sku> findMapByOriginId(List<String> orginIds, Long tenantId) {
        Map<String, Sku> map = new HashMap<String, Sku>();
        if (ListUtil.isNullOrEmpty(orginIds)) {
            return map;
        }

        List<Sku> skus = this.skuDAO.getSkuByOrigIds(orginIds, tenantId);
        for (Sku sku : skus) {
            map.put(sku.getOriginalId(), sku);
        }
        return map;
    }

    @Override
    public boolean isExistsByProductCode(String productCode, Long tenantId) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("productCode", productCode);
        params.put("tenantId", tenantId);
        return skuDAO.isExists(params, null);
    }

    @Override
    public Sku getSkuByCodeOrBarcode(String productCode, String barcode) {
        List<Sku> skuList = skuDAO.getSkuByBarcode(barcode);
        if (CollectionUtils.isEmpty(skuList)) {
            SkuFilter filter = new SkuFilter();
            filter.setTenantId(ParamUtil.getCurrentTenantId());
            filter.setProductCode(productCode);
            skuList = skuDAO.findByFilter(filter);
        }
        return CollectionUtils.isEmpty(skuList) ? null : skuList.get(0);
    }

    @Override
    public Sku getByCode(String productCode) {
        SkuFilter filter = new SkuFilter();
        filter.setTenantId(ParamUtil.getCurrentTenantId());
        filter.setProductCode(productCode);
        List<Sku> skuList = skuDAO.findByFilter(filter);
        return CollectionUtils.isEmpty(skuList) ? null : skuList.get(0);
    }

    /**
     * 计算货品等级 非效期品不处理
     *
     * @param dtos
     */
    @Override
    public void calculateGradeInfo(List<SkuGradeInfoDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        Set<Long> skuIds =
                dtos.stream().map(SkuGradeInfoDTO::getSkuId).collect(Collectors.toSet());
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", skuIds);
        List<Sku> skus = skuDAO.findListByProperty(params);
        if (CollectionUtils.isEmpty(skus)) {
            throw new MasterException(QA_SKU_NOT_EXIST);
        }
        Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getId,
                x -> x, (b1, b2) -> b1));
        params.clear();
        params.put("goodsCode", skus.stream().map(x -> x.getOriginalId()).collect(Collectors.toList()));
        List<ValidityGoodsRule> rules = validityGoodsRuleDAO.findListByProperty(params);
        Map<String, ValidityGoodsRule> ruleMap = rules.stream().collect(Collectors.toMap(ValidityGoodsRule::getGoodsCode,
                x -> x, (b1, b2) -> b1));
        Sku sku;
        ValidityGoodsRule rule;
        SkuGradeInfoDTO skuGradeInfoDTO;
        for (int i = 0; i < dtos.size(); i++) {
            skuGradeInfoDTO = dtos.get(i);
            sku = skuMap.get(skuGradeInfoDTO.getSkuId());
            rule = ruleMap.get(sku.getProductCode());
            if (YesNo.NO.getValue().toString().equals(sku.getShelfLifeFlg())) {
                skuGradeInfoDTO.setGoodsGrade(InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_BEST
                        .getBatchGoodsGradeType().toString());
                continue;
            } else if (Objects.isNull(rule) || Objects.isNull(rule.getNotificationIntervalTo())
                    || Objects.isNull(rule.getNotificationIntervalFrom())) {
                log.error("货品效期规则未配置或预警范围未维护，skuId={}, skuName={},barcode={}",
                        sku.getId(), sku.getProductCname(), sku.getEan13());
                throw new MasterException(MasterException.ERROR_GET_PRODUCT_VALIDITY, sku.getProductCode());
            }

            long expiry = DateUtil.between(DateUtil.beginOfDay(new Date()),
                    DateUtil.parseDate(skuGradeInfoDTO.getLotatt02()), DateUnit.DAY, false);
            String goodsGrade;
            // 大于预警期最大值
            if (expiry > rule.getNotificationIntervalTo()) {
                goodsGrade = InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_BEST.getBatchGoodsGradeType().toString();
            } else if (expiry > rule.getNotificationIntervalFrom()) { // 在预警期之间
                goodsGrade =
                        InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_NOTIFICATION.getBatchGoodsGradeType().toString();
            } else {// 在警告器期间
                goodsGrade = InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_WARNING.getBatchGoodsGradeType().toString();
            }
            skuGradeInfoDTO.setGoodsGrade(goodsGrade);
        }
    }

    /**
     * 计算货品等级 非效期品不处理
     *
     * @param dtos
     */
    @Override
    public void calculateGradeInfoForJob(List<SkuGradeInfoDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        Set<Long> skuIds =
                dtos.stream().map(SkuGradeInfoDTO::getSkuId).collect(Collectors.toSet());
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", skuIds);
        List<Sku> skus = skuDAO.findListByProperty(params);
        if (CollectionUtils.isEmpty(skus)) {
            throw new MasterException(QA_SKU_NOT_EXIST);
        }
        Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getId,
                x -> x, (b1, b2) -> b1));
        params.clear();
        params.put("goodsCode", skus.stream().map(x -> x.getOriginalId()).collect(Collectors.toList()));
        List<ValidityGoodsRule> rules = validityGoodsRuleDAO.findListByProperty(params);
        Map<String, ValidityGoodsRule> ruleMap = rules.stream().collect(Collectors.toMap(ValidityGoodsRule::getGoodsCode,
                x -> x, (b1, b2) -> b1));
        Sku sku;
        ValidityGoodsRule rule;
        SkuGradeInfoDTO skuGradeInfoDTO;
        for (int i = 0; i < dtos.size(); i++) {
            skuGradeInfoDTO = dtos.get(i);
            sku = skuMap.get(skuGradeInfoDTO.getSkuId());
            rule = ruleMap.get(sku.getProductCode());
            if (YesNo.NO.getValue().toString().equals(sku.getShelfLifeFlg())) {
                skuGradeInfoDTO.setGoodsGrade(InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_BEST
                        .getBatchGoodsGradeType().toString());
                continue;
            } else if (Objects.isNull(rule) || Objects.isNull(rule.getNotificationIntervalTo())
                    || Objects.isNull(rule.getNotificationIntervalFrom())) {
                log.error("货品效期规则未配置或预警范围未维护，skuId={}, skuName={},barcode={}",
                        sku.getId(), sku.getProductCname(), sku.getEan13());
                continue;
            }

            long expiry = DateUtil.between(DateUtil.beginOfDay(new Date()),
                    DateUtil.parseDate(skuGradeInfoDTO.getLotatt02()), DateUnit.DAY, false);
            String goodsGrade;
            // 大于预警期最大值
            if (expiry > rule.getNotificationIntervalTo()) {
                goodsGrade = InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_BEST.getBatchGoodsGradeType().toString();
            } else if (expiry > rule.getNotificationIntervalFrom()) { // 在预警期之间
                goodsGrade =
                        InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_NOTIFICATION.getBatchGoodsGradeType().toString();
            } else {// 在警告器期间
                goodsGrade = InventoryBatchGoodsGradeEnum.GOODS_GRADE_ZP_WARNING.getBatchGoodsGradeType().toString();
            }
            skuGradeInfoDTO.setGoodsGrade(goodsGrade);
        }
    }

    @Override
    @Transactional
    public void batchDeleteByEan13(List<String> barcodes) {
        skuDAO.batchDeleteByBarcodes(barcodes);
    }
}
