package com.daxia.wms.master.service;

import com.daxia.framework.common.util.DataPage;
import com.daxia.wms.master.MasterException;
import com.daxia.wms.master.dao.SkuDAO;
import com.daxia.wms.master.entity.Sku;
import com.daxia.wms.master.entity.SkuLocationOperationMap;
import com.daxia.wms.master.entity.dto.SkuGradeInfoDTO;
import com.daxia.wms.master.filter.SkuFilter;
import jxl.Sheet;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 货主产品信息Service
 */
public interface SkuService {

    
	/**
	 * 查询指定分页的Sku信息
	 * @param skuFilter
	 * @param startIndex
	 * @param pageSize
	 * @return
	 */
	DataPage<Sku> querySku(SkuFilter skuFilter, int startIndex, int pageSize);

	/**
	 * 获取指定ID的sku
	 * @param skuId
	 * @return
	 */
	Sku getSkuById(Long skuId);
	
	/**sku * 保存产品信息
     * @param sku
     */
    public void saveUpdate(Sku sku) throws MasterException;
    
    public void update(Sku sku);
    
    public List<Sku> querySku(SkuFilter skuFilter);
    
    /**根据商品条码查找sku
     * @param barcode
     */
    public List<Sku> queryBarcodeList(String barcode);
    
    /**
     * 通过商品编号查询商品
     * @param productCode 商品编号
     * @return
     */
    public Sku getSku(String productCode);
    
    public List<Sku> querySkuList(List<Long> ids);
    
    /**
     * 增加sku的库位操作记录
     * @param skuId 
     * @param locType 库位类型
     * @param locId 操作的库位id
     * @param trsType 交易类型
     * @param partitionId 库区编号
     */
    public void addLocationOperateRecord(Long skuId, String locType, Long locId, String trsType, Long partitionId);
    
    /**
     * 获得某一个sku在指定交易类型中的最后一次操作的库位
     * @param skuId
     * @param locTypes 操作的库位类型，存储区还是拣货区
     * @param trsTypes 操作的交易类型
     */
    public SkuLocationOperationMap getLastOperateLocation(Long skuId, Long partitionId, String[] locTypes, String... trsTypes);
    
    public SkuLocationOperationMap getLastOperateLocations(Long skuId, Long partitionId, String locType[],
    		Set<String> cycleClassSet, String priceClass, String... trsTypes);

	SkuDAO getSkuDAO();
	
    /**
     * 根据商品条码/商品编码找其SkuId，不会返回Null
     * @param code 商品条码/商品编码
     * @return
     */
    public List<Long> findSkuIdByCode(String code);
    
    public Sku getSkuByCodeAndBarcode(String code,String barcode);
    
    /**
     * 根据商品编码查询skus
     * @param codes
     * @return
     */
    public List<Sku> findSkusByProcuctCodes(List<String> codes);
    
    /**
     * 是否需要序列号管理。
     * @param sku
     * @return
     */
    public boolean needSerialScan(Sku sku);
    
    public List<Sku> getSkuByBarcode(String barcode);
    
    public void updateSkuTiHiInfo(Long skuId,Integer hiQty,Integer tiQty);
    
    /**
     * 从exl里保存商品
     */
    public void saveSkuFromSheet(Sheet sheet,Set<String> errorSet) ;
    
    public List<Sku> findSkusByName(String name);
    
    public List<Sku> getSkuByEan13(String barcode);

    /**
     * 根据外键id获取商品
     * @param id
     * @return
     */
    Sku getSkuByOrigId(String id);

    Sku getSkuByOrigId(String id, Long tenantId);

    void createDefaultPackageInfo(List<Sku> skus);

    void createDefaultPackageInfoBySku(Sku sku);

    Map<String,Long> findOriginIdMap(List<String> orginIds, Long tenantId);

    List<String> findByLocationId(Long locationId);

    void updateSkuLocationOperationByPickList(List<Long> idList, Long warehouseId);

    Map<String,Sku> findMapByOriginId(List<String> orginIds, Long tenantId);

    boolean isExistsByProductCode(String productCode, Long tenantId);

    Sku getSkuByCodeOrBarcode(String productCode, String barcode);

    Sku getByCode(String productCode);

    void calculateGradeInfo(List<SkuGradeInfoDTO> dtos);
    void calculateGradeInfoForJob(List<SkuGradeInfoDTO> dtos);

    void batchDeleteByEan13(List<String> barcodes);
}
