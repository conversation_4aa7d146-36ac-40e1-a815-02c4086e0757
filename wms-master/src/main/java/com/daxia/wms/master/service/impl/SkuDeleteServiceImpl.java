package com.daxia.wms.master.service.impl;

import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.master.service.SkuService;
import com.idanchuang.component.base.JsonResult;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import java.util.List;


@Name("SkuDeleteService")
@Path("/api/skuDelete")
@lombok.extern.slf4j.Slf4j
public class SkuDeleteServiceImpl {

    @In
    private SkuService skuService;

    @POST
    @Path("/batchDeleteByEan13")
    @Produces("application/json")
    public JsonResult batchDeleteByEan13(List<String> barcodes) {
        try {
            ParamUtil.setCurrentTenantCode("ABM");
            skuService.batchDeleteByEan13(barcodes);
            log.info("删除sku成功.size:{},barcodes:{}", barcodes, barcodes.size());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("Batch delete SKU failed", e);
            return JsonResult.failure(e.getMessage());
        }
    }

}
