package com.daxia.wms.master.dao;

import com.daxia.framework.common.dao.HibernateBaseDAO;
import com.daxia.framework.common.util.ParamUtil;
import com.daxia.wms.master.entity.Sku;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.criterion.Restrictions;
import org.jboss.seam.annotations.Name;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>产品信息DAO</p>
 */
@Name("com.daxia.wms.master.skuDAO")
@lombok.extern.slf4j.Slf4j
public class SkuDAO extends HibernateBaseDAO<Sku, Long> {
    private static final long serialVersionUID = 1L;
    
    /**
	 * 查找条码列表
	 */
	@SuppressWarnings("unchecked")
	public List<Sku> findBarcodeList(String barcode){
		String hql="from Sku o where o.productBarcode.barcodeLevel1=:barcode and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("barcode", barcode);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());

		return query.list();
	}
	
	/**
	 * 通过商品编码查询SKU
	 * @param productCode
	 * @return
	 */
	public Sku getSku(String productCode){
		String hql="from Sku o where o.productCode=:productCode and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setString("productCode", productCode);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return (Sku)query.uniqueResult();
	}

	public Double getVolume(Long id) {
        String hql = "select o.volume from md_sku o where o.id = :id";
        Query query = createSQLQuery(hql);
        query.setLong("id", id);
        Number volume = (Number) query.uniqueResult();
        if(volume == null){
            return null;
        }else{
            return volume.doubleValue();
        }
    }
	
    /**
	 * 查找sku
	 * @param code 商品编码
	 * @param barcode 商品条码
	 */
	public Sku getSkuByCodeAndBarcode(String code,String barcode) {
		String hql="from Sku o inner join fetch o.barCodes b where o.productCode = :code and b.barcodeLevel1 =:barcode and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("code", code);
		query.setParameter("barcode", barcode);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());

		query.setMaxResults(1);
		return (Sku) query.uniqueResult();
	}
	
	/**
	 * 根据商品编码查找skus
	 * @param codes 商品编码
	 */
	@SuppressWarnings("unchecked")
	public List<Sku> findSkusByCodes(List<String> codes) {
		String hql="from Sku o where o.productCode in (:codes) and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameterList("codes", codes);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return query.list();
	}
	
    /**
	 * 查找sku
	 * @param barcode 商品条码
	 */
	@SuppressWarnings("unchecked")
	public List<Sku> getSkuByBarcode(String barcode) {
        String hql="from Sku o inner join fetch o.barCodes b where b.barcodeLevel1 =:barcode and o.tenantId = :tenantId";
        Query query = createQuery(hql);
        query.setParameter("barcode", barcode);
        query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
        return query.list();
    }

    public void batchDeleteByBarcodes(List<String> barcodes) {
        String sql = "DELETE FROM md_sku WHERE ean13 IN (:barcodes) " +
				" and id not in (select sku_id from stk_batch_loc_lpn) " +
				" and id not in (select sku_id from doc_asn_detail) ";
        Query query = createSQLQuery(sql);
        query.setParameterList("barcodes", barcodes);
        query.executeUpdate();
    }
	
	@SuppressWarnings("unchecked")
	public List<Sku> getSkuByEan13(String barcode) {
		String hql="from Sku o  where o.ean13 =:barcode and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("barcode", barcode);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return query.list();
	}
	
	public void updateSkuTiHiInfo(Long skuId, Integer hiQty, Integer tiQty) {
		String hql = "update md_sku ms set ms.ti_qty = :tiQty,ms.hi_qty = :hiQty where id = :skuId ";
		Query query = createSQLQuery(hql);
		query.setParameter("skuId", skuId);
		query.setParameter("tiQty", tiQty);
		query.setParameter("hiQty", hiQty);
		query.executeUpdate();
	}
	
	@Deprecated
	public Long getSkuId () {
		String sql = "select SEQ_MD_SKU_ID.nextval from dual";
		Query query = createSQLQuery(sql);
		return ((BigDecimal)query.uniqueResult()).longValue();
	}
	
	@SuppressWarnings("unchecked")
	public List<Sku> findSkusByName(String name) {
		String hql="from Sku o where o.productCname like '%"+name+"%' and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return query.list();
	}

	public Sku getSkuByOrigId(String origId) {
		String hql = "SELECT o FROM Sku o WHERE o.originalId = :origId AND o.tenantId = :tenantId";
		return (Sku) createQuery(hql).setParameter("origId", origId).setParameter("tenantId", ParamUtil.getCurrentTenantId()).setMaxResults(1).uniqueResult();
	}
	public List<Sku> getSkuByOrigIds(List<String> origIds, Long tenantId) {
		String hql = " FROM Sku o WHERE o.originalId in (:origIds) AND o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameterList("origIds",origIds);
		query.setParameter("tenantId", tenantId);
		return query.list();
	}
	public List<Sku> getSkuByHelpCode(String helpCode){
		String hql="from Sku o where o.helpCode=:helpCode and o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setString("helpCode", helpCode);
		query.setParameter("tenantId", ParamUtil.getCurrentTenantId());
		return query.list();
	}

	public Sku getByOrigId(String originalId, Long tenantId){
		String hql="from Sku o where o.originalId =:originalId and  o.tenantId = :tenantId";
		Query query = createQuery(hql);
		query.setParameter("originalId",originalId);
		query.setParameter("tenantId", tenantId);
		return (Sku)query.setMaxResults(1).uniqueResult();
	}
	public List<Sku> findListByProperty(Map<String, Object> properties) {
		if (properties == null) {
			return null;
		}
		Criteria cri = getSession().createCriteria(Sku.class);
		for (Map.Entry<String, Object> entry : properties.entrySet()) {
			if (entry.getValue() instanceof Collection) {
				cri.add(Restrictions.in(entry.getKey(), (Collection) entry.getValue()));
			} else {
				cri.add(Restrictions.eq(entry.getKey(), entry.getValue()));
			}
		}
		return cri.list();
	}
}