<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:ui="http://java.sun.com/jsf/facelets"
    xmlns:h="http://java.sun.com/jsf/html"
    xmlns:f="http://java.sun.com/jsf/core"
    xmlns:s="http://jboss.com/products/seam/taglib"
    xmlns:a4j="http://richfaces.org/a4j"
    xmlns:rich="http://richfaces.org/rich"
    xmlns:fw="http://fw.daxia.com/ui"
    template="/templates/main.xhtml"
>
    <ui:define name="docTitle">订单结构预览_</ui:define>
    <ui:define name="title">
        <ui:include src="/templates/include/navigation.xhtml">
            <ui:param name="level_1_menu" value="出库管理" />
            <ui:param name="level_2_menu" value="订单结构预览" />
        </ui:include>
    </ui:define>
    <ui:define name="buttonTool"></ui:define>
    <ui:define name="searchPanel">
        <link
            rel="stylesheet"
            href="${facesContext.externalContext.requestContextPath}/res/chosen/chosen.min.css"
        />
        <link
            rel="stylesheet"
            type="text/css"
            href="${facesContext.externalContext.requestContextPath}/res/tipsy/tipsy.css"
        />
        <script
            type="text/javascript"
            src="${facesContext.externalContext.requestContextPath}/res/tipsy/jquery.tipsy.js"
        ></script>
        <table width="100%">
            <tbody>
                <tr>
                    <td>
                        <h:outputLabel value="商品条码" />
                    </td>
                    <td>
                        <h:inputTextarea
                            value="#{orderConstructionAction.orderConstructionFilter.barcode}"
                            style="width: 118px"
                            id="barcodeId"
                        />
                    </td>

                    <td>
                        <h:outputLabel value="组合条码" />
                    </td>
                    <td>
                        <h:inputTextarea
                            value="#{orderConstructionAction.orderConstructionFilter.barcodeByGroup}"
                            style="width: 118px"
                            id="barcodeByGroup"
                        />
                    </td>

                    <td>
                        <h:outputLabel value="通道号" />
                    </td>
                    <td>
                        <h:selectManyListbox
                            value="#{orderConstructionAction.orderConstructionFilter.aisles}"
                            style="width: 118px"
                            id="aisles"
                        >
                            <f:selectItems value="#{orderConstructionAction.aislesList}" />
                        </h:selectManyListbox>
                    </td>
                    <td>
                        <h:outputLabel value="配送商" />
                    </td>
                    <td>
                        <h:selectOneMenu
                            value="#{orderConstructionAction.orderConstructionFilter.carrierId}"
                            style="width: 118px"
                        >
                            <f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
                            <f:selectItems value="#{carrierSelector}" />
                        </h:selectOneMenu>
                    </td>

                    <td>
                        <h:outputLabel value="运输温度" />
                    </td>
                    <td>
                        <h:selectOneMenu
                            value="#{orderConstructionAction.orderConstructionFilter.transportWendy}"
                            style="width: 118px"
                        >
                            <f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
                            <f:selectItems
                                value="#{dictionary.getSelectItems('TRANSPORT_WENDY')}"
                            />
                        </h:selectOneMenu>
                    </td>

                    <td>
                        <h:outputLabel value="渠道" />
                    </td>
                    <td>
                        <h:selectOneMenu
                            id="channelCode"
                            value="#{orderConstructionAction.orderConstructionFilter.channelCode}"
                            onchange="on_channelChange(this);"
                            immediate="true"
                        >
                            <f:selectItem itemLabel="#{messages['global.title.pleaseChoice']}" />
                            <f:selectItems value="#{businessChannelSelector}" />
                        </h:selectOneMenu>
                    </td>

                    <td colspan="2" align="center">
                        <a4j:commandButton
                            value="#{messages['button.query']}"
                            id="btnQuery"
                            action="#{orderConstructionAction.buttonQuery()}"
                            onclick="if(!daxia.util.checkForm(this.form,this)){return false;}"
                            oncomplete="actbutton()"
                            styleClass="btnCla"
                            reRender="resultList,dataDiv"
                        ></a4j:commandButton>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h:outputLabel value="货品等级" />
                    </td>
                    <td>
                        <h:selectManyListbox
                            value="#{orderConstructionAction.orderConstructionFilter.goodsGrades}"
                            style="width: 118px"
                            id="goodsGrades"
                        >
                            <f:selectItems value="#{selectItemsService.getGeneWaveGoodsGrades()}" />
                        </h:selectManyListbox>
                    </td>

                    <td>
                        <h:outputLabel value="标记订单" />
                    </td>
                    <td>
                        <h:selectOneMenu
                            id="specialLabel"
                            value="#{orderConstructionAction.orderConstructionFilter.specialLabel}"
                            style="width: 118px"
                        >
                            <f:selectItems value="#{orderConstructionAction.specialLabelList}" />
                        </h:selectOneMenu>
                    </td>

                    <td>
                        <h:outputLabel value="商品种数From" />
                    </td>
                    <td>
                        <h:inputText
                            id="skuTypeQtyFm"
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.skuTypeQtyFm}"
                        />
                    </td>

                    <td>
                        <h:outputLabel value="商品种数To" />
                    </td>
                    <td>
                        <h:inputText
                            id="skuTypeQtyTo"
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.skuTypeQtyTo}"
                        />
                    </td>

                    <td>
                        <h:outputLabel value="件数From" />
                    </td>
                    <td>
                        <h:inputText
                            id="skuQtyFm"
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.skuQtyFm}"
                        />
                    </td>

                    <td>
                        <h:outputLabel value="件数To" />
                    </td>
                    <td>
                        <h:inputText
                            id="skuQtyTo"
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.skuQtyTo}"
                        />
                    </td>
                </tr>

                <tr>
                    

                    <td>
                        <h:outputLabel value="体积" />
                    </td>
                    <td>
                        <h:selectOneMenu
                            id="docVolumeType"
                            value="#{orderConstructionAction.orderConstructionFilter.docVolumeType}"
                            style="width: 118px"
                        >
                            <f:selectItems value="#{orderConstructionAction.docVolumeTypeList}" />
                        </h:selectOneMenu>
                    </td>

                    <td>
                        <h:outputLabel value="导入时间From" />
                    </td>
                    <td>
                        <h:inputText
                            id="createTimeFrom"
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.createTimeFrom}"
                            onclick="showDateTimePicker(this)"
                        >
                            <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                        </h:inputText>
                    </td>

                    <td>
                        <h:outputLabel value="导入时间To" />
                    </td>
                    <td>
                        <h:inputText
                            style="width: 118px"
                            value="#{orderConstructionAction.orderConstructionFilter.createTimeTo}"
                            onclick="showDateTimePicker(this)"
                        >
                            <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                        </h:inputText>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h:selectBooleanCheckbox
                            value="#{orderConstructionAction.orderConstructionFilter.pointGrade}"
                            id="pointGrade"
                        />
                        <h:outputLabel value="混货品等级" />
                    </td>
                </tr>

                <tr>
                    <td colspan="4">
                        <div style="display: flex; align-items: center">
                            <h:outputLabel value="普通单波次订单数量" />
                            <!-- 单选框组 -->
                            <h:selectOneRadio
                                id="radioGroup"
                                value="#{orderConstructionAction.orderConstructionFilter.radioSelection}"
                                style="margin-right: 10px"
                                onclick="turnPageCountDO('selectForm');"
                            >
                                <f:selectItem
                                    itemLabel="#{orderConstructionAction.orderConstructionFilter.regularOrderNum}"
                                    itemValue="#{orderConstructionAction.orderConstructionFilter.regularOrderNum}"
                                />
                                <f:selectItem itemLabel="其它" itemValue="0" />
                            </h:selectOneRadio>

                            <!-- 输入框，一直显示 -->
                            <h:inputText
                                id="customOrderQty"
                                value="#{orderConstructionAction.orderConstructionFilter.customOrderQty}"
                                style="width: 30px; margin-right: 10px"
                                onchange="turnPageCountDO('selectForm');"
                            />

                            <!-- 生成波次按钮 -->
                            <input
                                type="button"
                                value="生成波次"
                                class="btnCla"
                                style="
                                    height: 30px;
                                    line-height: 30px;
                                    padding: 0 10px;
                                    text-align: center;
                                    vertical-align: middle;
                                    display: inline-block;
                                "
                                onclick="if(daxia.util.checkBox.checkSelectOneMore(selectForm.id)){checkForGenerateWave('selectForm');};return false;"
                            />
                        </div>
                    </td>
                </tr>

                <tr>
                    <td colspan="5">
                        <div style="display: flex; align-items: center">
                            <!-- 提总单波次订单数量标签和输入框 -->
                            <h:outputLabel value="提总单波次订单数量" style="margin-right: 5px" />
                            <h:inputText
                                id="orderQty"
                                value="#{orderConstructionAction.orderConstructionFilter.orderQty}"
                                style="width: 50px; margin-right: 10px"
                            />

                            <!-- 提总单波次订单数量下限标签和输入框 -->
                            <h:outputLabel
                                value="提总单波次订单数量下限"
                                style="margin-right: 5px"
                            />
                            <h:inputText
                                id="orderQtyMin"
                                value="#{orderConstructionAction.orderConstructionFilter.orderQtyMin}"
                                style="width: 50px; margin-right: 10px"
                            />

                            <!-- 生成提总波次按钮 -->
                            <input
                                type="button"
                                value="生成提总波次"
                                class="btnCla"
                                style="
                                    height: 30px;
                                    line-height: 30px;
                                    padding: 0 10px;
                                    text-align: center;
                                    vertical-align: middle;
                                    display: inline-block;
                                "
                                onclick="if(daxia.util.checkBox.checkSelectOneMore(selectForm.id)){checkForBatchGenWave('selectForm');};return false;"
                            />
                        </div>
                    </td>
                </tr>
            </tbody>
            <a4j:jsFunction
                name="changeStructureType"
                action="#{orderConstructionAction.changeStructureType()}"
                reRender="dataDiv,resultList,selectForm"
                oncomplete="actbutton();"
            >
                <a4j:actionparam
                    name="structureType"
                    assignTo="#{orderConstructionAction.structureType}"
                />
            </a4j:jsFunction>
        </table>
        <script type="text/javascript">
            document.observe('dom:loaded', function (evt) {
                new Chosen($('searchForm:aisles'), { allow_single_deselect: true });
            });

            document.observe('dom:loaded', function (evt) {
                new Chosen($('searchForm:goodsGrades'), { allow_single_deselect: true });
            });

            function checkForGenerateWave(formId) {
                console.log(formId);
                openSse('generateWave', '/w/sse/bigWaveBySelect/gene/');
                const radioSelection = Array.from(
                    document.getElementsByName('searchForm:radioGroup'),
                ).find(radio => radio.checked);
                const customOrderQty = document.getElementsByName('searchForm:customOrderQty')[0];
                generateWave(radioSelection.value, customOrderQty.value);
            }

            function checkForBatchGenWave(formId) {
                var orderQty = document.getElementById('searchForm:orderQty').value; // 获取输入框中的值
                var orderQtyMin = document.getElementById('searchForm:orderQtyMin').value; // 获取输入框中的值
                console.log(orderQty);
                console.log(orderQtyMin);
                if (!orderQty.trim() || !orderQtyMin.trim()) {
                    alert('请先输入提总波次订单数及数量下限');
                    return;
                }
                openSse('batchWave', '/w/sse/batchwave/gene/');
                // 获取input元素的值
                batchGenWave(orderQty, orderQtyMin);
            }

            //<![CDATA[
            jQuery(function () {
                // 页面刷新事件，关闭所有 SSE
                window.addEventListener(
                    'beforeunload',
                    function () {
                        closeAllSseConnections();
                    },
                    false,
                );
            });

            const sseConnections = {}; // 用于存储多个 SSE 实例

            // 通用超时时间
            const sseTimeOut = 120000 * 5;

            // 关闭所有 SSE 连接
            function closeAllSseConnections() {
                for (const key in sseConnections) {
                    const connection = sseConnections[key];
                    if (connection.sse && connection.sse.readyState !== EventSource.CLOSED) {
                        connection.sse.close();
                        clearInterval(connection.interval);
                    }
                }
            }

            // 打开一个新的 SSE 连接
            function openSse(connectionId, url) {
                // 如果已有该 ID 的连接，先关闭
                if (sseConnections[connectionId]) {
                    const existingConnection = sseConnections[connectionId];
                    if (existingConnection.sse) {
                        existingConnection.sse.close();
                        clearInterval(existingConnection.interval);
                    }
                }

                // 初始化新的连接对象
                const newConnection = {
                    sse: new EventSource(url, { withCredentials: true }),
                    interval: null,
                    timeOutTimestamp: null,
                    successCount: 0,
                };

                // 记录到全局对象中
                sseConnections[connectionId] = newConnection;

                // 设置事件监听
                newConnection.sse.onopen = function () {
                    newConnection.timeOutTimestamp = sseTimeOut + new Date().valueOf();
                    newConnection.successCount = 0;

                    // 根据 connectionId 清空对应 div
                    const targetDiv =
                        connectionId === 'generateWave'
                            ? '#resultList_body_1'
                            : '#resultList_body_2';
                    jQuery(targetDiv).html('');

                    // 定时器检测超时
                    newConnection.interval = setInterval(function () {
                        const timestamp = new Date().valueOf();
                        if (timestamp > newConnection.timeOutTimestamp) {
                            if (newConnection.sse.readyState !== EventSource.CLOSED) {
                                newConnection.sse.close();
                                clearInterval(newConnection.interval);
                            }
                        }
                    }, 1000);
                };

                newConnection.sse.onmessage = function (evt) {
                    const data = evt.data.replaceAll('"', '');

                    // 根据 connectionId 动态选择 div
                    const targetDiv =
                        connectionId === 'generateWave'
                            ? '#resultList_body_1'
                            : '#resultList_body_2';
                    jQuery(targetDiv).append(
                        "<div class='rich-messages-label' style='font-size: 13px'>" +
                            data +
                            '</div>',
                    );

                    if (data === '执行完成') {
                        newConnection.sse.close();
                        clearInterval(newConnection.interval);
                    }

                    document.getElementById('searchForm:btnQuery').click();
                };

                // 异常关闭连接
                newConnection.sse.onerror = function (evt) {
                    console.log(evt);
                    if (newConnection.sse.readyState !== EventSource.CLOSED) {
                        newConnection.sse.close();
                        clearInterval(newConnection.interval);
                        document.getElementById('searchForm:btnQuery').click();
                    }
                };
            }

            //]]>
        </script>
    </ui:define>
    <ui:define name="content">
        <a4j:form id="selectForm">
            <rich:panel
                id="resultList"
                styleClass="panelCla"
                bodyClass="panelBody"
                headerClass="panelHead"
            >
                <h:panelGroup id="doShow">
                    <input
                        type="button"
                        value="单品单件"
                        class="btnCla"
                        id="btn1"
                        style="
                            height: 30px;
                            line-height: 30px;
                            padding: 0 10px;
                            text-align: center;
                            vertical-align: middle;
                            display: inline-block;
                        "
                        onclick="queryByStructureType(1, this)"
                    />
                    <input
                        type="button"
                        value="单品多件"
                        class="btnCla"
                        id="btn2"
                        style="
                            height: 30px;
                            line-height: 30px;
                            padding: 0 10px;
                            text-align: center;
                            vertical-align: middle;
                            display: inline-block;
                        "
                        onclick="queryByStructureType(2, this)"
                    />
                    <input
                        type="button"
                        value="提总单"
                        class="btnCla"
                        id="btn3"
                        style="
                            height: 30px;
                            line-height: 30px;
                            padding: 0 10px;
                            text-align: center;
                            vertical-align: middle;
                            display: inline-block;
                        "
                        onclick="queryByStructureType(3, this)"
                    />
                    <input
                        type="button"
                        value="杂品单"
                        class="btnCla"
                        id="btn4"
                        style="
                            height: 30px;
                            line-height: 30px;
                            padding: 0 10px;
                            text-align: center;
                            vertical-align: middle;
                            display: inline-block;
                        "
                        onclick="queryByStructureType(4, this)"
                    />
                    <input
                        type="button"
                        value="组合品订单"
                        class="btnCla"
                        id="btn5"
                        style="
                            height: 30px;
                            line-height: 30px;
                            padding: 0 10px;
                            text-align: center;
                            vertical-align: middle;
                            display: inline-block;
                        "
                        onclick="queryByStructureType(5, this)"
                    />
                    <rich:spacer width="10" />
                    <h:outputText value="选中的已分配订单数量:" style="font-size: 15px" />
                    <rich:spacer width="10" />
                    <h:outputText
                        styleClass="countSpan"
                        value="0"
                        id="totalCount"
                        style="color: red; font-size: 20px"
                    />
                    <rich:spacer width="10" />
                    <h:outputText value="预计生成普通波次:" style="font-size: 15px" />
                    <rich:spacer width="10" />
                    <h:outputText
                        styleClass="wareCount"
                        value="0"
                        id="wareCount"
                        style="color: red; font-size: 20px"
                    />
                    <!--                    <rich:spacer width="10" />-->
                    <!--                    <h:outputText value="订货总数量:" style="font-size:15px"/>-->
                </h:panelGroup>

                <script type="text/javascript">
                    var atButton = null;

                    function queryByStructureType(number, element) {
                        changeStructureType(number);
                        atButton = element;
                        document.getElementById('activeButtonIdHidden').value = element.id; // 保存当前按钮的 ID
                    }

                    function actbutton() {
                        // 获取所有按钮
                        var buttons = document.querySelectorAll('.btnCla');
                        // 移除所有按钮的 activeBtn 类
                        buttons.forEach(function (btn) {
                            btn.classList.remove('activeBtn');
                        });

                        // 从隐藏输入元素中获取保存的 activeButtonId
                        var activeButtonId = document.getElementById('activeButtonIdHidden').value;
                        if (activeButtonId) {
                            var activeButton = document.getElementById(activeButtonId);
                            if (activeButton) {
                                activeButton.classList.add('activeBtn');
                            }
                        }
                        turnPageCountDO('selectForm');
                    }
                </script>

                <style>
                    .btnCla {
                        /* 默认按钮样式 */
                        background-color: #f0f0f0;
                        color: black;
                    }

                    .btnCla.activeBtn {
                        /* 选中的按钮样式 */
                        background-color: #007bff;
                        color: white;
                        border: 1px solid #007bff;
                    }
                </style>
                <s:div
                    id="dataDiv"
                    styleClass="tableContainer"
                    rendered="#{orderConstructionAction.dataPage.dataList.size() > 0}"
                >
                    <rich:dataTable
                        value="#{orderConstructionAction.dataPage.dataList}"
                        rowClasses="listTableSingular,listTableDual"
                        var="item"
                        width="100%"
                        id="resultTable"
                        rowKeyVar="row"
                    >
                        <rich:column headerClass="listTableHeadLeft">
                            <f:facet name="header">
                                <f:verbatim>
                                    <input
                                        type="checkbox"
                                        align="left"
                                        id="selectAll"
                                        onclick="daxia.util.checkBox.checkAll(this,this.form.id);turnPageCountDO('selectForm');"
                                    />
                                </f:verbatim>
                            </f:facet>
                            <h:selectBooleanCheckbox
                                onclick="turnPageCountDO('selectForm');"
                                value="#{orderConstructionAction.selectedMap[item.ids]}"
                            />
                        </rich:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="序号" />
                            </f:facet>
                            <h:outputText value="#{row + 1}" />
                        </h:column>

                        <rich:column headerClass="headerCenter" style="text-align: center">
                            <f:facet name="header">
                                <h:outputText value="商品条码" />
                            </f:facet>
                            <div
                                class="scrollable-column"
                                style="width: 400px; white-space: nowrap"
                            >
                                <h:outputText value="#{item.barcodes}" />
                            </div>
                        </rich:column>

                        <rich:column headerClass="headerCenter" style="text-align: center">
                            <f:facet name="header">
                                <h:outputText value="商品名称" />
                            </f:facet>
                            <div
                                class="scrollable-column"
                                style="width: 500px; white-space: nowrap"
                            >
                                <h:outputText value="#{item.goodsName}" />
                            </div>
                        </rich:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="未分配订单数量" />
                            </f:facet>
                            <h:commandLink
                                value="#{item.unallocatedOrderNum}"
                                onclick="showOrderNos('#{item.unallocatedOrderNos}'); return false;"
                            />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="已分配订单数量" />
                            </f:facet>
                            <h:commandLink
                                styleClass="allocatedOrderNum"
                                value="#{item.allocatedOrderNum}"
                                onclick="showOrderNos('#{item.allocatedOrderNos}'); return false;"
                            />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="配送商" />
                            </f:facet>
                            <h:outputText value="#{item.carrierName}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="运输温度" />
                            </f:facet>
                            <h:outputText value="#{item.transportWendy}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="渠道" />
                            </f:facet>
                            <h:outputText value="#{item.channelName}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="货品等级" />
                            </f:facet>
                            <h:outputText value="#{item.cycleClass}">
                                <fw:dict dictionary="DO_GRADE_VIEW" />
                            </h:outputText>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="标记订单" />
                            </f:facet>
                            <h:outputText value="#{item.specialLabel}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="商品种数" />
                            </f:facet>
                            <h:outputText value="#{item.skuTypeCount}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="件数" />
                            </f:facet>
                            <h:commandLink
                                styleClass="expectedQty"
                                value="#{item.expectedQty}"
                                onclick="showOrderDetail('#{item.allocatedOrderNos}','#{item.unallocatedOrderNos}'); return false;"
                            />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="体积" />
                            </f:facet>
                            <h:outputText value="#{item.docVolumeType}" />
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="已分配的通道" />
                            </f:facet>
                            <h:outputText value="#{item.allocatedPassage}" />
                        </h:column>
                    </rich:dataTable>
                </s:div>
                <s:div
                    styleClass="alignCenter"
                    rendered="#{orderConstructionAction.dataPage.noDataFound}"
                >
                    <h:graphicImage value="/res/images/no_result.png" style="border: 0px" />
                </s:div>

                <fw:pagination
                    listBean="#{orderConstructionAction}"
                    action="query"
                    pageModel="#{orderConstructionAction.dataPage}"
                    reRender="resultList"
                    hookFunction="isCheck()"
                ></fw:pagination>

                <a4j:jsFunction
                    name="generateWave"
                    action="#{orderConstructionAction.generateWave()}"
                    requestDelay="100"
                    reRender="dataDiv,resultList"
                >
                    <a4j:actionparam
                        name="radioSelection"
                        assignTo="#{orderConstructionAction.orderConstructionFilter.radioSelection}"
                    />
                    <a4j:actionparam
                        name="customOrderQty"
                        assignTo="#{orderConstructionAction.orderConstructionFilter.customOrderQty}"
                    />
                </a4j:jsFunction>

                <a4j:jsFunction
                    name="batchGenWave"
                    action="#{orderConstructionAction.batchGenWave()}"
                    requestDelay="100"
                    reRender="dataDiv,resultList"
                >
                    <a4j:actionparam
                        name="orderQty"
                        assignTo="#{orderConstructionAction.orderConstructionFilter.orderQty}"
                    />
                    <a4j:actionparam
                        name="orderQtyMin"
                        assignTo="#{orderConstructionAction.orderConstructionFilter.orderQtyMin}"
                    />
                </a4j:jsFunction>
            </rich:panel>
            <div style="display: flex; justify-content: space-between; gap: 20px">
                <!-- 左侧区域 -->
                <div id="resultList_left" style="flex: 1; border: 1px solid #ccc; padding: 10px">
                    <h1>生成普通波次结果</h1>
                    <div id="resultList_body_1"></div>
                </div>
                <!-- 右侧区域 -->
                <div id="resultList_right" style="flex: 1; border: 1px solid #ccc; padding: 10px">
                    <h1>生成团购波次结果</h1>
                    <div id="resultList_body_2"></div>
                </div>
            </div>
        </a4j:form>
        <input type="hidden" id="activeButtonIdHidden" />

        <rich:modalPanel
            id="orderModal"
            label="订单明细（仅展示前20条，可复制全部）"
            style="
                position: relative;
                min-width: 200px;
                max-width: 90%;
                max-height: 90vh;
                overflow: auto;
            "
        >
            <f:facet name="header">
                <h:panelGroup styleClass="alignCenter">
                    <h:outputText value="订单明细（仅展示前20条，复制全部）"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <i
                        class="fa fa-window-close fa-fw"
                        style="color: #fff; margin: 5px"
                        id="hidelink"
                    />
                    <rich:componentControl
                        for="orderModal"
                        attachTo="hidelink"
                        operation="hide"
                        event="onclick"
                    />
                </h:panelGroup>
            </f:facet>

            <table id="orderTable" style="margin-left: auto; margin-right: auto">
                <thead>
                    <th>
                        订单明细
                        <button id="copyButton" style="margin-left: 10px">复制订单</button>
                    </th>
                </thead>
                <tbody></tbody>
            </table>
        </rich:modalPanel>

        <rich:modalPanel
            id="orderDetailModal"
            label="商品数量明细"
            style="
                position: relative;
                min-width: 300px;
                max-width: 80%;
                max-height: 80vh;
                overflow: auto;
                border-radius: 8px;
            "
        >
            <f:facet name="header">
                <h:panelGroup styleClass="alignCenter">
                    <h:outputText
                        value="商品数量明细"
                        style="font-weight: bold; font-size: 16px; color: #333"
                    />
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <i
                        class="fa fa-window-close fa-fw"
                        style="color: #666; cursor: pointer; font-size: 20px; margin: 5px"
                        id="hidelink1"
                    />
                    <rich:componentControl
                        for="orderDetailModal"
                        attachTo="hidelink1"
                        operation="hide"
                        event="onclick"
                    />
                </h:panelGroup>
            </f:facet>

            <a4j:outputPanel id="orderDetail" style="padding: 10px">
                <table class="styled-table">
                    <thead>
                        <tr>
                            <th>商品条码</th>
                            <th>商品名称</th>
                            <th>件数</th>
                        </tr>
                    </thead>
                    <tbody id="orderDetailBody">
                        <ui:repeat value="#{orderConstructionAction.orderDetails}" var="item">
                            <tr>
                                <td><h:outputText value="#{item.barcode}" /></td>
                                <td><h:outputText value="#{item.barcodeName}" /></td>
                                <td><h:outputText value="#{item.expectedQty}" /></td>
                            </tr>
                        </ui:repeat>
                    </tbody>
                </table>
            </a4j:outputPanel>
        </rich:modalPanel>

        <script type="text/javascript">
            var orders = [];

            function showOrderDetail(orderNos, unOrderNos) {
                var firstOrderNo;
                if (orderNos != null &amp;&amp; orderNos != '') {
                    firstOrderNo = orderNos.split(',')[0];// 将传入的订单号字符串分割成数组
                } else {
                    firstOrderNo = unOrderNos.split(',')[0];
                }
                showOrderDetailModal(firstOrderNo); // 显示模态面板
            }


            function showOrderNos(orderNos) {
                orders = orderNos.split(',');  // 将传入的订单号字符串分割成数组
                renderTable();  // 调用修改后的渲染表格函数
                showOrderModal(); // 显示模态面板
            }

            function renderTable() {
                var maxDisplay = 20;  // 设置最大显示数量为20
                var displayOrders = orders.slice(0, maxDisplay);  // 取前20个订单，如果不足20个，则取全部
                var tableBody = document.getElementById('orderTable').getElementsByTagName('tbody')[0];
                tableBody.innerHTML = '';  // 清空表格当前内容

                displayOrders.forEach(function (order) {
                    var row = tableBody.insertRow();
                    var cell = row.insertCell(0);
                    cell.innerText = order;  // 使用innerText防止XSS
                });
            }

            document.getElementById('copyButton').addEventListener('click', function () {
                var textToCopy = orders.join('\n');  // 使用换行符连接数组元素
                navigator.clipboard.writeText(textToCopy).then(function () {
                    // 更改按钮文字和样式作为反馈
                    var button = document.getElementById('copyButton');
                    button.textContent = '复制成功';
                    button.style.backgroundColor = 'green';
                    button.style.color = 'white';

                    // 1秒后恢复原样
                    setTimeout(function () {
                        button.textContent = '复制订单';
                        button.style.backgroundColor = '';
                        button.style.color = '';
                    }, 1000);

                }, function (err) {
                    console.error('复制失败:', err);
                    // 提示失败信息
                    alert('复制失败，请重试');
                });
            });

            //<![CDATA[
            function turnPageCountDO(formId) {
                // 获取选中复选框的订单数量总和
                const totalOrderCount = calculateTotalOrderCount(formId);

                // 更新订单总数到页面
                updateElementContent(".countSpan", totalOrderCount);

                // 获取单波次最大订单数
                const maxOrderPerWave = getMaxOrderPerWave();

                // 计算波次数量
                let waveCount = calculateWaveCount(totalOrderCount, maxOrderPerWave);

                // 更新波次数量到页面
                updateElementContent(".wareCount", waveCount);
            }

            // 计算选中复选框的订单总数
            function calculateTotalOrderCount(formId) {
                const selectedCheckboxes = jQuery(`#${formId}:resultTable`).find("tr input[type=checkbox]:gt(0):checked");
                let totalOrderCount = 0;

                jQuery.each(selectedCheckboxes, function (_, checkbox) {
                    const orderNumText = jQuery(checkbox).closest("tr").find(".allocatedOrderNum").text();
                    const orderNum = parseInt(orderNumText, 10) || 0; // 防止非数字报错
                    totalOrderCount += orderNum;
                });

                return totalOrderCount;
            }

            // 获取单波次最大订单数
            function getMaxOrderPerWave() {
                // 获取选中的单选框值
                const selectedRadio = Array.from(document.getElementsByName("searchForm:radioGroup")).find(radio => radio.checked);
                let orderQty = selectedRadio ? parseInt(selectedRadio.value, 10) : 0;

                // 如果选中的是自定义值，获取输入框值
                if (orderQty === 0) {
                    const customOrderInput = document.getElementsByName("searchForm:customOrderQty")[0];
                    orderQty = customOrderInput ? parseInt(customOrderInput.value, 10) : 0;
                }

                return orderQty || 1; // 防止 `orderQty` 为 0
            }

            // 计算波次数量
            function calculateWaveCount(totalOrderCount, maxOrderPerWave) {
                if (maxOrderPerWave <= 0) return 0; // 避免除以 0

                const waveCount = Math.floor(totalOrderCount / maxOrderPerWave);
                return waveCount === 0 && totalOrderCount > 0 ? 1 : waveCount;
            }

            // 更新页面中的元素内容
            function updateElementContent(selector, content) {
                jQuery(selector).html(content);
            }

            //]]>
        </script>

        <a4j:jsFunction
            name="showOrderModal"
            oncomplete="Richfaces.showModalPanel('orderModal');"
        ></a4j:jsFunction>

        <a4j:jsFunction
            name="showOrderDetailModal"
            action="#{orderConstructionAction.queryOrderDetails}"
            reRender="orderDetail"
            oncomplete="Richfaces.showModalPanel('orderDetailModal');"
        >
            <a4j:actionparam
                name="firstOrderNo"
                assignTo="#{orderConstructionAction.orderConstructionFilter.firstOrderNo}"
            />
        </a4j:jsFunction>

        <style>
            #orderTable {
                width: 90%; /* 让表格宽度充满容器 */
                border-collapse: collapse; /* 折叠边框使其看起来更紧凑 */
                margin: 20px 0; /* 在表格上下添加一些间距 */
            }

            #orderTable th {
                font-size: 13px; /* 表头字体稍大一点突出显示 */
                background-color: #f0f0f0; /* 更淡的背景色 */
            }

            #orderTable {
                font-family: Arial, sans-serif; /* 设置字体 */
                font-size: 12px; /* 设置字体大小 */
            }

            #orderTable th,
            #orderTable td {
                text-align: center; /* 将文本对齐设置为居中 */
                padding: 4px; /* 维持适当的填充 */
                border: 1px solid #ccc; /* 使用更淡的颜色强调轻巧感 */
            }

            #orderTable th,
            #orderTable td {
                vertical-align: middle; /* 垂直居中内容 */
            }

            .scrollable-column {
                overflow-x: auto; /* 启用水平滚动条 */
            }

            .scrollable-column::-webkit-scrollbar {
                height: 8px; /* 滚动条高度 */
                background: #f0f0f0; /* 滚动条背景颜色 */
            }

            .scrollable-column::-webkit-scrollbar-thumb {
                background: #888; /* 滚动条滑块颜色 */
                border-radius: 4px; /* 滑块圆角 */
            }

            .scrollable-column::-webkit-scrollbar-thumb:hover {
                background: #555; /* 滑块悬停时颜色 */
            }

            /* 优化表格整体样式 */
            .styled-table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
                font-size: 14px;
                text-align: left;
                color: #333;
                border: 1px solid #ddd;
            }

            .styled-table th,
            .styled-table td {
                padding: 10px 15px;
                border: 1px solid #ddd;
            }

            /* 表头样式 */
            .styled-table th {
                background-color: #f4f4f4;
                font-weight: bold;
                text-align: center;
            }

            /* 数据行样式 */
            .styled-table tr:nth-child(even) {
                background-color: #f9f9f9;
            }

            .styled-table tr:hover {
                background-color: #f1f1f1;
                cursor: pointer;
            }

            /* 弹窗的标题栏样式 */
            .rich-mpnl-header {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                text-align: center;
                padding: 10px;
                border-radius: 8px 8px 0 0;
            }

            /* 弹窗整体边框和背景 */
            .rich-mpnl-body {
                background-color: #ffffff;
                border-radius: 0 0 8px 8px;
            }

            /* 关闭按钮样式 */
            .fa-window-close {
                transition: all 0.3s ease;
            }

            .fa-window-close:hover {
                color: #ff4d4d;
                transform: scale(1.1);
            }
        </style>
    </ui:define>
</ui:composition>

