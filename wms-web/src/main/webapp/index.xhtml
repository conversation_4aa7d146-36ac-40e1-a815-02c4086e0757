<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:s="http://jboss.com/products/seam/taglib"
      xmlns:rich="http://richfaces.org/rich"
      xmlns:a4j="http://richfaces.org/a4j" xmlns:c="http://java.sun.com/jstl/core">
<f:view>
    <title class="webTitle">首页</title>
    <link rel="icon" href="/res/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" type="text/css" href="${facesContext.externalContext.requestContextPath}/res/font-awesome-4.7.0/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="${facesContext.externalContext.requestContextPath}/res/css/global.css" />

    <script type="text/javascript"
            src="${facesContext.externalContext.requestContextPath}/js/base64.js?v=${appConfig.getBuildVersion()}"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/a4j/g/3_3_3.Final/daxia/js/fw.js.jsf?v=${appConfig.getBuildVersion()}"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/a4j/g/3_3_3.Final/daxia/js/JSValidate.js.jsf?v=${appConfig.getBuildVersion()}"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/res/js/prototype_1.7.2.js"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/js/LodopFuncs.js?v=${appConfig.getBuildVersion()}"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/js/wms.js?v=${appConfig.getBuildVersion()}"></script>
    <script type="text/javascript" src="${facesContext.externalContext.requestContextPath}/js/lodopPrint.js?v=${appConfig.getBuildVersion()}"></script>
    <script type="application/javascript"  src="${facesContext.externalContext.requestContextPath}/js/jquery-1.11.1.min.js" />
    <link rel="stylesheet" type="text/css"
          href="${facesContext.externalContext.requestContextPath}/js/tab/jquery-tab.css" />
    <link rel="stylesheet" type="text/css"
          href="${facesContext.externalContext.requestContextPath}/res/css/addition.css" />
    <script type="text/javascript"
            src="${facesContext.externalContext.requestContextPath}/js/tab/jquery-tab-new.js"></script>
    <link rel="stylesheet" href="${facesContext.externalContext.requestContextPath}/css/b.tabs.css" />
    <script type="application/javascript"  src="${facesContext.externalContext.requestContextPath}/js/b.tabs.js" />
    <script type="application/javascript" >
        jQuery.noConflict();
    </script>
    <nav class="navbar navbar-default">
        <div class="container-fluid">
        <div class="navbar-header">
            <h:form id="warehouseSelectForm">
                <div class="navbar-brand-container">
                    <h:selectOneMenu value="#{warehouseSwitcher.selectedWarehouseId}"
                                     styleClass="warehouse-selector"
                                     onchange="document.getElementById('warehouseSelectForm:switchBtn').click();">
                        <f:selectItems value="#{warehouseSwitcher.warehouseOptions}"  />
                    </h:selectOneMenu>
                    <h:commandButton action="#{warehouseSwitcher.switchWarehouse}"
                                     style="display:none;"
                                     id="switchBtn" />
                    <style type="text/css">
                        option {
                            color: black;
                        }
                    </style>
                </div>
            </h:form>
        </div>
        <div class="navbar-collapse">
            <ul class="nav nav-pills navbar-nav">
                <c:forEach items="#{sysMenuComponent.permissionMenuList}" var="menuItem" varStatus="status">
                    <c:if test="#{identity.hasPermission(menuItem.permissionCode) or (menuItem.permissionCode == null)}">
                        <li class="dropdown">
                            <a href="#">
                                <i class="fa fa-#{menuItem.icon} fa-fw"/>
                                <h:outputText value="#{menuItem.menuName}"/>
                            </a>

                            <c:choose>
                                <c:when test="#{menuItem.subMenus.size() > 4 and status.index > 2}">
                                    <c:set var="styleLeft" value="left: -200px;"/>
                                </c:when>
                                <c:otherwise>
                                    <c:set var="styleLeft" value=""/>
                                </c:otherwise>
                            </c:choose>

                            <s:div styleClass="dropdown-menu" rendered="#{menuItem.subMenus.size() > 0}" style="#{styleLeft}padding-left:0px;padding-right:0px;">
                                <div>
                                    <div class="nav-sub">
                                        <c:forEach items="#{menuItem.subMenus}" var="subItem">
                                            <c:set var="showMenu" value="false"/>
                                            <c:forEach items="#{subItem.subMenus}" var="leafItem">
                                                <c:if test="${identity.hasPermission(leafItem.permissionCode) or (leafItem.permissionCode == null)}">
                                                    <c:set var="showMenu" value="true"/>
                                                </c:if>
                                            </c:forEach>
                                            <s:div styleClass="nav-sub-item" rendered="#{showMenu}">
                                                <h5>
                                                    <h:outputText value="#{subItem.menuName}"/>
                                                </h5>
                                                <ul class="menuSideBar">
                                                    <a4j:repeat value="#{subItem.subMenus}" var="leafItem">
                                                        <c:choose>
                                                            <c:when test="#{pageConfig.get('global.promote.except.permissions',0) ne null and pageConfig.get('global.promote.except.permissions',0) ne ''}">
                                                                <c:set var="showSubMenu" value="true"/>
                                                                <h:panelGroup rendered="#{ (leafItem.permissionCode == null)  or (identity.hasPermission(leafItem.permissionCode) and !config.getByDelimit('global.promote.except.permissions',',',0).contains(leafItem.permissionCode))}">
                                                                    <li mid="#{leafItem.menuName}" funurl="#{facesContext.externalContext.requestContextPath.concat(leafItem.url)}">
                                                                        <h:outputLink value="javascript:void(0);">
                                                                            <h:outputText value="#{leafItem.menuName}" tabindex="-1" />
                                                                        </h:outputLink>
                                                                    </li>
                                                                </h:panelGroup>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <h:panelGroup rendered="#{identity.hasPermission(leafItem.permissionCode) or (leafItem.permissionCode == null)}">
                                                                    <li mid="#{leafItem.menuName}" funurl="#{facesContext.externalContext.requestContextPath.concat(leafItem.url)}">
                                                                        <a href="javascript:void(0)" tabindex="-1">
                                                                            #{leafItem.menuName}
                                                                        </a>
                                                                    </li>
                                                                </h:panelGroup>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </a4j:repeat>
                                                </ul>
                                            </s:div>
                                        </c:forEach>
                                    </div>
                                </div>
                            </s:div>
                        </li>
                    </c:if>
                </c:forEach>
            </ul>
            <h:form id="topRightForm">
                <ul class="nav navbar-nav navbar-right " >

                    <li class="dropdown moveTaskNotify"  mid="库存移动单" funurl="${facesContext.externalContext.requestContextPath}/stock/stockShiftList.xhtml">
                        <s:div rendered="#{stockShiftListService.findAvailableMoveTask('40').size() > 0}">
                            <a href="javascript:void(0)" tabindex="-1">
                                <i class="fa fa-bell fa-fw"/>
                                <h:outputText style="color: red" value="待移库任务数：#{stockShiftListService.findAvailableMoveTask('40').size() }"/>
                            </a>
                        </s:div>
                    </li>

                    <li class="dropdown">
                        <a href="#"><i class="fa fa-user fa-fw"/><h:outputText value="#{identity.userAccount.displayName}"/><i class="fa fa-angle-down fa-fw"/></a>
                        <ul class="dropdown-menu">

                            <li><a id="fullscreenLink" href="javascript:wms.util.fullscreen();jQuery('#fullscreenLink').addClass('hide');jQuery('#existFullscreen').removeClass('hide')">全屏</a></li>
                            <li><a id="existFullscreen" class="hide" href="javascript:wms.util.existFullscreen();jQuery('#existFullscreen').addClass('hide');jQuery('#fullscreenLink').removeClass('hide')">退出全屏</a></li>
                            <li class="changePwd"  mid="修改密码" funurl="${facesContext.externalContext.requestContextPath}/system/userAccount/modifyPassword.xhtml">
                                <a href="javascript:void(0)" tabindex="-1">
                                    修改密码
                                </a>
                            </li>
                            <li><h:commandLink action="#{authenticator.logout}" value="退出" rendered="#{identity.isLoggedIn()}"/></li>
                        </ul>
                    </li>
                </ul>
            </h:form>
        </div>
    </div>
    </nav>
    <!-- 标签页区域 -->
    <div class="span10" id="mainFrameTabs">

        <!-- Nav tabs -->
        <ul class="nav nav-tabs" role="tablist">
            <!-- 设置默认的首页标签页，设置noclose样式，则不会被关闭 -->
            <li role="presentation" class="active noclose">
                <a href="#bTabs_首页" data-toggle="tab">首页
                    <button type="button" class="navTabsCloseBtn" title="关闭" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span></button>
                </a>
            </li>
        </ul>

        <!-- Tab panes -->
        <div class="tab-content">
            <!-- 默认标签页（首页）的内容区域 -->
            <div class="tab-pane active" id="bTabs_首页">
                <iframe frameborder="0" scrolling="yes" style="width:100%;height:100%;border:0px;" src="/home.xhtml"></iframe>
            </div>
        </div>
        <style type="text/css">
            .bTabs .nav-tabs>li>a {
                font-size: 1.3em;
            }
            .warehouse-selector {
                background: transparent !important;
                border: none !important;
                color: #fff !important;
                font-size: 18px !important;
                font-weight: bold !important;
                padding: 0 !important;
                margin: 0 !important;
                box-shadow: none !important;
                outline: none !important;
                cursor: pointer;
                width: 153px;
            }
            .warehouse-selector:focus {
                background: transparent !important;
                border: none !important;
                box-shadow: none !important;
                outline: none !important;
            }
            .navbar-brand-container {
                display: flex;
                align-items: center;
                padding-top: 10px;
            }
        </style>
    </div>
    <script type="application/x-javascript">
        //导航区域项目点击增加标签页处理
        jQuery('a',jQuery('.menuSideBar')).bind('click', function(e) {
            
            e.stopPropagation();
            var li = jQuery(this).closest('li');
            var menuId = jQuery(li).attr('mid');
            var url = jQuery(li).attr('funurl');
            jQuery('.webTitle').text(menuId)
            var title = menuId;
            jQuery('#mainFrameTabs').bTabsAdd(menuId,title,url,checkLogin);
        });
        jQuery('a',jQuery('.moveTaskNotify')).bind('click', function(e) {
            e.stopPropagation();
            var li = jQuery(this).parent().parent();
            var menuId = jQuery(li).attr('mid');
            var url = jQuery(li).attr('funurl');
            var title = menuId;
            //校验登录是否超时，通常使用ajax访问服务端测试登录是否超时
            jQuery('#mainFrameTabs').bTabsAdd(menuId,title,url,checkLogin);
        });
        jQuery('a',jQuery('.changePwd')).bind('click', function(e) {
            e.stopPropagation();
            var li = jQuery(this).parent();
            var menuId = jQuery(li).attr('mid');
            var url = jQuery(li).attr('funurl');
            var title = menuId;
            //校验登录是否超时，通常使用ajax访问服务端测试登录是否超时
            jQuery('#mainFrameTabs').bTabsAdd(menuId,title,url,checkLogin);
        });

        //插件的初始化
        jQuery('#mainFrameTabs').bTabs({
            //登录界面URL，用于登录超时后的跳转
            'sortable':true,
            'loginUrl' : '/login.xhtml',
            //用于初始化主框架的宽度高度等，另外会在窗口尺寸发生改变的时候，也自动进行调整
            'resize' : function(){
                //这里只是个样例，具体的情况需要计算
                jQuery('#mainFrameTabs').height(890);
            }
        });
        function checkLogin (){
            let isLogin=true;
            let setting={
                url:"/w/report/custom/?pandasEditPage=1",
                async:false,
                contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                type:"POST",
                success:function (result,status,xhr){
                    if(result.message=='unlogin'){
                        isLogin=false;
                    }
                },
                error:function (xhr,status,error){
                    console.info(result,status,xhr)
                    isLogin=false;
                }
            }
            jQuery.ajax(setting)
            return isLogin;
        };
    </script>
</f:view>
</html>
