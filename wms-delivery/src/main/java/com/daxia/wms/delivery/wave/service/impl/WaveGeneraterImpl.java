package com.daxia.wms.delivery.wave.service.impl;

import com.dangdang.ddframe.rdb.sharding.exception.ShardingJdbcException;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.log.LogUtil;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.service.SequenceGeneratorService;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.*;
import com.daxia.wms.Keys;
import com.daxia.wms.OrderLogConstants;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.container.service.ContainerMgntService;
import com.daxia.wms.delivery.container.service.PktContainerService;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.deliveryorder.service.DoAllocateService;
import com.daxia.wms.delivery.deliveryorder.service.OrderLogService;
import com.daxia.wms.delivery.deliveryorder.service.SpecialDoLabelService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.pick.service.PickTaskService;
import com.daxia.wms.delivery.wave.dao.WaveDAO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.service.WaveGenerater;
import com.daxia.wms.exp.service.ExpFacadeService;
import com.daxia.wms.master.entity.Region;
import com.daxia.wms.master.service.RegionRelayOrderService;
import com.daxia.wms.master.service.RegionService;
import com.daxia.wms.master.service.SortingBinService;
import com.daxia.wms.stock.stock.dto.OrderStockDTO;
import com.daxia.wms.stock.stock.service.IOperator;
import com.daxia.wms.stock.stock.service.StockService;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Transactional;

import java.util.*;

import static com.daxia.wms.delivery.deliveryorder.service.impl.DoAllocateServiceImpl.PRIVATE_CHANEL_CODE_SET;

/**
 * 波次生成策略实现
 */
@Name("waveGenerater")
@lombok.extern.slf4j.Slf4j
public class WaveGeneraterImpl implements WaveGenerater {
    @In
    private SequenceGeneratorService sequenceGeneratorService;
    @In
    private PickTaskService pickTaskService;
    @In
    private PickHeaderService pickHeaderService;
    @In
    private WaveDAO waveDAO;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private SortingBinService sortingBinService;
    @In
    private DoAllocateService doAllocateService;
    @In
    private RegionService regionService;
    @In
    private StockService stockService;
    @In("waveOperator")
    private IOperator waveOperator;
    @In
    private ExpFacadeService expFacadeService;
    @In
    private RegionRelayOrderService regionRelayOrderService;
    @In
    private SpecialDoLabelService specialDoLabelService;
    @In
    private OrderLogService orderLogService;
    @In(create = true)
    private ContainerGenerateDispatcher containerGenerateDispatcher;
    @In
    ContainerMgntService containerMgntService;
    @In
    PktContainerService pktContainerService;

    /**
     * 生成波次
     *
     * @param doList           发货单 list
     * @param doIdList         发货单Id list
     * @param regionIds        拣货区域ID list
     * @param sortingBinId     分拣柜Id
     * @param waveType         波次类型
     * @param wavePriority
     * @param isRecommend
     * @param autoWaveType     自动波次类型，如果非自动波次，请选择AutoWaveType.NULL
     * @param isSemiAuto
     * @param ruleDetailId
     * @param criteriaDetailId
     * @return 波次头
     */
    @Override
    @Loggable
    @Transactional
    public WaveHeader generateWave(List<DeliveryOrderHeader> doList, List<Long> doIdList, List<Long> regionIds,
            Long sortingBinId, String waveType, Integer wavePriority, Boolean isRecommend, AutoWaveType autoWaveType,
            Boolean isSemiAuto, Long ruleDetailId, Long criteriaDetailId) throws Exception {
        WaveHeader waveHeader = new WaveHeader();
        // 设置波次编号
        String generateSeq = getWaveNoByAutoWaveType(autoWaveType);

        String waveNo = sequenceGeneratorService.generateSequenceNo(generateSeq, ParamUtil.getCurrentWarehouseId());
        log.debug("Generate Wave:[waveNo: {}]", waveNo);
        waveHeader.setNotes(getWaveNotesByDeliveryOrderList(doList));
        waveHeader.setDoCount(doIdList.size());
        waveHeader.setWaveNo(waveNo);
        // 设置波次状态 -跟出库单类型一致
        waveHeader.setWaveStatus(WaveStatus.ALLALLOCATED.getValue());
        waveHeader.setWaveType(waveType);
        // 设置拣货单数量
        waveHeader.setPktCount(Long.valueOf(regionIds.size()));
        // 设置波次优先级
        waveHeader.setPriority(wavePriority);
        waveHeader.setEmergencyFlag(getEmergencyFlagFromDoList(doList));
        waveHeader.setIsRecommend(isRecommend ? YesNo.YES.getValue() : YesNo.NO.getValue());
        waveHeader.setIsAuto(
                isSemiAuto != null ? (isSemiAuto ? YesNo.YES.getValue() : YesNo.NO.getValue()) : YesNo.NO.getValue());
        waveHeader.setAutoType(autoWaveType.getValue());
        waveHeader.setRuleDetailId(ruleDetailId);
        waveHeader.setCriteriaDetailId(criteriaDetailId);
        DeliveryOrderHeader firstDo = doList.get(0);
        waveHeader.setVolumeType(firstDo.getVolumeType());
        // 更新波次发票信息
        deliveryOrderService.updateWaveInvoiceInfo(waveHeader, doList, null);

        String autoScript = SystemConfig.getConfigValue("delivery.wave.autoFlagScript",
                ParamUtil.getCurrentWarehouseId());
        if (StringUtil.isNotEmpty(autoScript)) {
            MvelUtil.eval(autoScript, ImmutableBiMap.of("waveHeader", (Object) waveHeader));
        }

        // 设置分拣柜号
        if (sortingBinId != null) {
            waveHeader.setSortGridId(sortingBinId);
            sortingBinService.addWaveQty(sortingBinId);
        }
        // 设置波次的最早预计出库时间
        waveHeader.setEstDoFinishTime(getEarliestDoFinishTimeInDos(doList));

        // 设置打印购物清单标记
        waveHeader.setDoNeedPrint(calculateDoNeedPrint(doList, autoWaveType));

        waveDAO.saveOrUpdate(waveHeader);

        // 设置劳动力管理拣货单是否可索取逻辑
        Integer manageSwitch = SystemConfig.getConfigValueInt("LABOR_FORCE_MANAGE_SWITCH",
                ParamUtil.getCurrentWarehouseId());
        Region firstRegion = null;
        if (LaborForceSwitch.RELAY.getValue().equals(manageSwitch)) {
            firstRegion = regionRelayOrderService.findFirstPickRegion(regionIds);
        }

        // 根据区域生成拣货单
        if (checkEmergencyFlag(waveHeader)) {
            Boolean isAvailable = isRequestAvailable(waveHeader, manageSwitch, firstRegion, regionIds.get(0));
            // 存在EA的拣货任务
            if (pickTaskService.existUnitPickTask(regionIds, doIdList, true)) {
                genPickForEmergency(doIdList, waveHeader, regionIds, isAvailable, true);
            }

            // 存在包装的拣货任务
            if (pickTaskService.existUnitPickTask(regionIds, doIdList, false)) {
                genPickForEmergency(doIdList, waveHeader, regionIds, isAvailable, false);
            }
        } else {
            for (Long regionId : regionIds) {
                Boolean isAvailable = isRequestAvailable(waveHeader, manageSwitch, firstRegion, regionId);
                // 存在EA的拣货任务
                if (pickTaskService.existUnitPickTask(regionId, doIdList, true)) {
                    splitAndGenPick(waveHeader, doIdList, regionId, isAvailable, true);
                }

                // 存在包装的拣货任务
                if (pickTaskService.existUnitPickTask(regionId, doIdList, false)) {
                    splitAndGenPick(waveHeader, doIdList, regionId, isAvailable, false);
                }
            }
        }

        // 将doHeader改为已跑波次并更新doHeader所属波次,分配分拣格,分配库存
        updateDo(doList, waveHeader.getId());
        // 物理删除AllocateHeader和AllocateDetail
        doAllocateService.removeAllocate(doIdList);
        // 自动生成波次中，使本次异常不影响继续生成波次，继续循环生成波次
        waveDAO.getSession().flush();

        // 记录日志
        for (DeliveryOrderHeader doHeader : doList) {
            orderLogService.saveLog(doHeader,
                    OrderLogConstants.OrderLogType.WAVE_COMPLETE.getValue(),
                    ResourceUtils.getDispalyString(OrderLogConstants.ORDER_OPERATE_LOG_WAVE_COMPLETE, null,
                            waveHeader.getWaveNo()));
        }
        if (Constants.YesNo.YES.getValue().equals(waveHeader.getInvoiceFlag())) {
            deliveryOrderService.sendInvoice2Oms(waveHeader);
        }

        // 启用了电子标签
        if (Config.isDefaultFalse(Keys.Delivery.pick_useDPS, Config.ConfigLevel.WAREHOUSE)) {
            if (WaveType.WAVE_NORMAL.getValue().equals(waveHeader.getWaveType())
                    && !waveHeader.getAutoType().equals(AutoWaveType.BATCH_GROUP.getValue())) {
                pktContainerService.createTemp(waveHeader);
            }
        }

        return waveHeader;
    }

    private boolean checkEmergencyFlag(WaveHeader waveHeader) {
        return YesNo.YES.getValue().equals(waveHeader.getEmergencyFlag()) &&
                Config.isDefaultTrue(Keys.Delivery.wave_emergency_not_spit_region, Config.ConfigLevel.WAREHOUSE);
    }

    private Integer getEmergencyFlagFromDoList(List<DeliveryOrderHeader> doList) {
        Integer preFlag = null;
        for (int i = 0; i < doList.size(); i++) {
            if (i == 0) {
                preFlag = doList.get(i).getEmergencyFlag();
            } else if (!Objects.equals(preFlag, doList.get(i).getEmergencyFlag())) {
                return Integer.valueOf(0);
            }
        }
        return preFlag;
    }

    private void splitAndGenPick(WaveHeader waveHeader, List<Long> doIdList, Long regionId, Boolean isAvailable,
            boolean isEaTask) {
        if (splitPick(regionId, waveHeader)) {
            for (Long doId : doIdList) {
                List<Long> genDoIds = Lists.<Long>newArrayList(doId);
                if (existTask(genDoIds, regionId, isEaTask)) {
                    genPick(genDoIds, waveHeader, regionId, isAvailable, isEaTask);
                }
            }
        } else {
            genPick(doIdList, waveHeader, regionId, isAvailable, isEaTask);
        }
    }

    private boolean existTask(List<Long> genDoIds, Long regionId, boolean isEa) {
        return pickTaskService.existTaskByDo(genDoIds, regionId, isEa);
    }

    // 这里暂时实现按区域配置切单规则，后面需要进行整合；
    private Boolean splitPick(Long regionId, WaveHeader waveHeader) {
        // 紧急波次，不按区域切分拣货单
        if (checkEmergencyFlag(waveHeader)) {
            return false;
        }
        Region region = regionService.get(regionId);
        return region != null && Config.getByDelimit(Keys.Delivery.pick_splitRegions, Config.ConfigLevel.WAREHOUSE)
                .contains(region.getRegionCode());
    }

    private void genPick(List<Long> doIdList, WaveHeader waveHeader, Long regionId, Boolean isAvailable,
            Boolean isEaTask) {
        // 生成拣货单
        PickHeader pktHeader = pickHeaderService.createPktHeader(waveHeader.getId(), regionId, isAvailable,
                isEaTask ? PickHeader.PKT_TYPE_PCS : PickHeader.PKT_TYPE_UNIT);
        // 关联拣货任务和波次
        pickTaskService.relate(waveHeader.getId(), pktHeader.getId(), doIdList, regionId, isEaTask);
        // 生成流水容器
        // containerGenerateDispatcher.genContainerRef(pktHeader);
    }

    private void genPickForEmergency(List<Long> doIdList, WaveHeader waveHeader, List<Long> regionIdList,
            Boolean isAvailable, Boolean isEaTask) {
        // 生成拣货单
        PickHeader pktHeader = pickHeaderService.createPktHeader(waveHeader.getId(), regionIdList.get(0), isAvailable,
                isEaTask ? PickHeader.PKT_TYPE_PCS : PickHeader.PKT_TYPE_UNIT);
        // 关联拣货任务和波次
        for (Long regionId : regionIdList) {
            pickTaskService.relate(waveHeader.getId(), pktHeader.getId(), doIdList, regionId, isEaTask);
        }
        // 生成流水容器
        // containerGenerateDispatcher.genContainerRef(pktHeader);
    }

    // 是否可索取
    // 现在是按照波次类型和波次细分类型来索取任务的，这里放开限制，让用户通过分组索取
    private boolean isRequestAvailable(WaveHeader waveHeader, Integer manageSwitch,
            Region firstRegion, Long regionId) {
        // 紧急波次，不能索取
        if (waveHeader.getEmergencyFlag().equals(Integer.valueOf(1))) {
            return false;
        }

        if (specialDoLabelService.isRequestAvailable(waveHeader.getAutoType())) {
            // 合单订单波次所有拣货单均不可索取
            // 波次细分类型对应特殊标记不可索取
            if (!LaborForceSwitch.RELAY.getValue().equals(manageSwitch)) {// 非接力均可获取
                return true;
            } else if (null == firstRegion || firstRegion.getId().equals(regionId)) {
                // 若为接力模式拣货，则只有第一个区域对应拣货单可索取；
                return true;
            }
        }

        return false;
    }

    /**
     * 更新生成波次的发货单集合(将doHeader改为已跑波次并更新doHeader所属波次,分配分拣格,分配库存)
     *
     * @param doList 生成波次的发货单集合
     * @param waveId 波次Id
     */
    @Transactional
    public void updateDo(List<DeliveryOrderHeader> doList, Long waveId) throws Exception {
        log.debug("Generate Wave, updateDo: [waveId: {}]", waveId);

        int sortGridNo = 1;
        List<Long> idList = Lists.newArrayList();
        for (DeliveryOrderHeader doHeader : doList) {
            DeliveryOrderHeader header = deliveryOrderService.getDoHeaderById(doHeader.getId());
            if (!DoStatus.ALLALLOCATED.getValue().equals(header.getStatus())) {
                throw new DeliveryException(DeliveryException.DO_STATUS_NOTMATCH);
            }
            deliveryOrderService.updateDOs4Wave(doHeader, waveId, sortGridNo);
            sortGridNo++;
            idList.add(doHeader.getId());
            // 增加同步接口数据到定时任务表
            // expFacadeService.sendDo2OmsCreateDatas(doHeader.getId(),
            // doHeader.getStatus(), doHeader.getWaveFlag(), null, doHeader.getDoType());
        }

        try {
            updateWaveStock(idList);
        } catch (Exception e) {
            if (e instanceof ShardingJdbcException
                    && e.getMessage().indexOf("Data truncation: Out of range value for column 'qty_available'") > -1) {
                throw new DeliveryException(DeliveryException.ALLOCATE_OVER_STOCK);
            }
            throw e;
        }
    }

    /**
     * 更新库存，扣主库存，删除allocating
     *
     */
    private void updateWaveStock(List<Long> idList) {
        OrderStockDTO stockDTO = new OrderStockDTO();
        stockDTO.setDocIdList(idList);
        waveOperator.setStockDto(stockDTO);
        stockService.operateStock(waveOperator);
    }

    /**
     * 取得doList中最早的预计出库时间WaveGeneraterImpl
     *
     * @param doList
     * @return
     */
    private Date getEarliestDoFinishTimeInDos(List<DeliveryOrderHeader> doList) {
        Date minTime = null;
        for (DeliveryOrderHeader doHeader : doList) {
            Date tempTime = doHeader.getPlanShipTime();
            if (null != tempTime) {
                if (null == minTime || minTime.after(tempTime)) {
                    minTime = tempTime;
                }
            }
        }
        return minTime;
    }

    private String getWaveNotesByDeliveryOrderList(List<DeliveryOrderHeader> doList) {
        Boolean isWaveNotesByOrders = SystemConfig.configIsOpen("wave.note.by.do", ParamUtil.getCurrentWarehouseId());
        if (!isWaveNotesByOrders) {
            return StringUtils.EMPTY;
        }
        Map<String, String> map = new HashMap<String, String>();
        StringBuilder notes = new StringBuilder("");
        for (DeliveryOrderHeader deliveryOrderHeader : doList) {
            if (StringUtils.isNotBlank(deliveryOrderHeader.getSellerRemark())
                    && !map.containsKey(deliveryOrderHeader.getSellerRemark())) {
                notes.append(deliveryOrderHeader.getSellerRemark()).append(",");
                map.put(deliveryOrderHeader.getSellerRemark(), deliveryOrderHeader.getSellerRemark());
            }
        }
        String note = notes.toString();
        if (StringUtils.isNotBlank(note)) {
            note = note.substring(0, note.length() - 1);
        }
        return note;
    }

    /**
     * 根据波次细分规则获取波次编码
     * 
     * @param autoWaveType 波次细分规则
     * @return 波次编码
     */
    private String getWaveNoByAutoWaveType(AutoWaveType autoWaveType) {
        String waveSeq = SequenceName.WAVENO.getValue();
        if (null == autoWaveType) {
            return waveSeq;
        }
        switch (autoWaveType) {
            case BATCH_GROUP:
                waveSeq = SequenceName.BATCH_WAVENO.getValue();
                break;
            case MILA:
                waveSeq = SequenceName.MILA_WAVENO.getValue();
                break;
            case BOND_GIFT:
                waveSeq = SequenceName.GIFT_WAVENO.getValue();
                break;
            default:
                waveSeq = SequenceName.WAVENO.getValue();
        }
        return waveSeq;
    }

    /**
     * 计算打印购物清单标记
     * 打标逻辑：
     * 1. 普通波次内单个p单的Max(商品种数)<=8，不打标 do_need_print =0
     * 2. 普通波次内单个p单的Max(商品种数)>8，打标 do_need_print =1
     * 3. 提总波次，不打标 do_need_print =0
     *
     * @param doList       发货单列表
     * @param autoWaveType 波次类型
     * @return 打印购物清单标记 0-不打标 1-打标
     */
    private Integer calculateDoNeedPrint(List<DeliveryOrderHeader> doList, AutoWaveType autoWaveType) {
 
        // 提总波次，不打标
        if (AutoWaveType.BATCH_GROUP.equals(autoWaveType) ) {
            return 0;
        }


        // 普通波次，根据商品种数判断
        Integer maxSkuCount = 0;
        for (DeliveryOrderHeader doHeader : doList) {
            // 获取订单的商品种数（存储在userDeffine5字段中）
            String userDeffine5 = doHeader.getUserDeffine5();
            if (userDeffine5 != null && !userDeffine5.trim().isEmpty()) {
                try {
                    Integer skuCount = Integer.parseInt(userDeffine5.trim());
                    if (skuCount > maxSkuCount) {
                        maxSkuCount = skuCount;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid userDeffine5 value for DO {}: {}", doHeader.getDoNo(), userDeffine5);
                }
            }
        }

        // 如果最大商品种数>8，打标；否则不打标
        return maxSkuCount > 8 ? 1 : 0;
    }
}