package com.daxia.wms.delivery.wave.action;

import com.daxia.framework.common.action.PagedListBean;
import com.daxia.framework.common.annotation.BusinessExceptionProxy;
import com.daxia.framework.common.annotation.Loggable;
import com.daxia.framework.common.util.*;
import com.daxia.framework.system.util.WmsUtil;
import com.daxia.wms.ConfigKeys;
import com.daxia.wms.Constants;
import com.daxia.wms.Constants.YesNo;
import com.daxia.wms.Keys;
import com.daxia.wms.PageConfig;
import com.daxia.wms.delivery.DeliveryException;
import com.daxia.wms.delivery.deliveryorder.entity.DeliveryOrderHeader;
import com.daxia.wms.delivery.deliveryorder.filter.DoHeaderFilter;
import com.daxia.wms.delivery.deliveryorder.service.DeliveryOrderService;
import com.daxia.wms.delivery.invoice.service.InvoiceJinrenService;
import com.daxia.wms.delivery.pick.entity.PickHeader;
import com.daxia.wms.delivery.pick.filter.PickHeaderFilter;
import com.daxia.wms.delivery.pick.service.PickHeaderService;
import com.daxia.wms.delivery.print.dto.PrintCfg;
import com.daxia.wms.delivery.print.helper.WaybillPrintHelper;
import com.daxia.wms.delivery.print.service.PrintDoService;
import com.daxia.wms.delivery.print.service.PrintPickLabelService;
import com.daxia.wms.delivery.print.service.PrintService;
import com.daxia.wms.delivery.print.service.carton.PrintCartonDispatcher;
import com.daxia.wms.delivery.recheck.service.TempCartonService;
import com.daxia.wms.delivery.wave.dto.WaveDetailDTO;
import com.daxia.wms.delivery.wave.entity.WaveHeader;
import com.daxia.wms.delivery.wave.filter.WaveHeaderFilter;
import com.daxia.wms.delivery.wave.service.WaveService;
import com.daxia.wms.print.dto.PrintData;
import com.daxia.wms.master.component.BusinessCenterComponent;
import com.google.gson.Gson;
import org.jboss.seam.ScopeType;
import org.jboss.seam.annotations.Create;
import org.jboss.seam.annotations.In;
import org.jboss.seam.annotations.Name;
import org.jboss.seam.annotations.Scope;
import org.jboss.seam.annotations.security.Restrict;
import org.json.JSONArray;

import java.io.IOException;
import java.util.*;

/**
 * 波次业务Action
 */
@Name("com.daxia.wms.delivery.waveAction")
@Restrict("#{identity.hasPermission('delivery.wave')}")
@Scope(ScopeType.PAGE)
@BusinessExceptionProxy
@lombok.extern.slf4j.Slf4j
public class WaveAction extends PagedListBean<WaveHeader> {

    private static final long serialVersionUID = -7854447028541058795L;

    private WaveHeader wave;// 波次计划头信息实体
    private Long waveId; // 头信息主键
    private boolean initialized = false;
    private WaveHeaderFilter waveHeaderFilter;
    private Long removeDoId;// 删除的订单id
    private List<WaveDetailDTO> doHeaders;
    private String printType;
    private String printData = "[]";
    private String waybillPrintData = "[]";
    private String printContent;
    private String printContent1;
    private String printContent2;
    private int doSize;
    private int orient = 1;
    private Boolean printFlag = false;

    /**
     * 发货单打印配置参数
     */
    private PrintCfg doPrintCfg;

    @In
    private WaveService waveService;
    @In
    private DeliveryOrderService deliveryOrderService;
    @In
    private PickHeaderService pickHeaderService;
    @In
    private PrintService printService;
    @In
    private PrintCartonDispatcher printCartonDispatcher;
    @In
    private InvoiceJinrenService invoiceJinrenService;
    @In(create = true)
    private PrintDoService printDoService;
    @In(create = true)
    private PrintPickLabelService printPickLabelService;
    @In
    private TempCartonService tempCartonService;
    @In
    private BusinessCenterComponent businessCenterComponent;
    /**
     * 是否使用A5纸打印拣货单
     */
    private Integer isPrintPickTaskByA5;

    private Integer cartonPrintPoint;

    public Boolean needPrintCarton() {
        return Config.isDefaultFalse(Keys.Delivery.gene_temp_carton, Config.ConfigLevel.WAREHOUSE) ;
    }

    @Create
    @Loggable
    public void init() {
        this.waveHeaderFilter = new WaveHeaderFilter();
        this.isPrintPickTaskByA5 = printService.isPrintPickTaskByA5();
        this.cartonPrintPoint = PageConfig.getInt(ConfigKeys.CARTON_PRINT_POINT,
                Config.ConfigLevel.WAREHOUSE.getValue());
    }

    /**
     * 根据WaveHeaderFilter查询波次
     */
    @Override
    public void query() {
        waveHeaderFilter.getOrderByMap().put("createdAt", "desc");
        this.buildOrderFilterMap(waveHeaderFilter);
        DataPage<WaveHeader> dataPage = waveService.findWaveHeaderByFilter(waveHeaderFilter, getStartIndex(),
                getPageSize());
        this.populateValues(dataPage);
    }

    /**
     * 查看波次详细信息
     */
    public void view() {
        if (!initialized) {
            this.wave = waveService.getWave(waveId);
            this.doHeaders = this.deliveryOrderService.qureyWaveDetail(waveId);

            // 转换渠道编码为渠道名称
            if (doHeaders != null && !doHeaders.isEmpty()) {
                convertChannelCodeToName(doHeaders);
                this.doSize = doHeaders.size();
                if (doSize > 1) {
                    Collections.sort(doHeaders, new Comparator<WaveDetailDTO>() {
                        @Override
                        public int compare(WaveDetailDTO o1, WaveDetailDTO o2) {
                            return o1.getSortGridNo().compareTo(o2.getSortGridNo());
                        }
                    });
                }
            }
            this.initialized = true;
        }
    }

    /**
     * 从波次中取消DO
     */
    public void removeDo() {
        this.deliveryOrderService.remove(removeDoId);
        int rowNum = ListUtil.getPosFromListIdValue(this.doHeaders, removeDoId, "doId");
        this.doHeaders.remove(rowNum);
        this.setOperationStatus(rowNum);
        this.doSize = this.doHeaders.size();
        this.sayMessage(MESSAGE_SUCCESS);
    }

    /**
     * 明细页面打印
     */
    public void detailPrint() {
        this.getSelectedMap().put(this.getWaveId(), true);
        this.print();
    }

    /**
     * 根据打印类型打印
     */
    public void print() {
        this.printData = "[]";
        this.printContent = "";
        this.waybillPrintData="";
        this.printContent1="";
        this.printContent2="";

        orient = 1;

        int type = 0;
        if ("carton".equals(this.printType)) {
            printCarton();
            type = WaveHeader.FLAG_PRINT_CARTON;
        } else if ("pick".equals(this.printType)) {
            printPick();
            type = WaveHeader.FLAG_PRINT_PICK;
        } else if ("do".equals(this.printType)) {
            printDo();
            type = WaveHeader.FLAG_PRINT_DO;
            // 打印出库单时消费"打印购物清单"标记
            // consumeShoppingListPrintFlag();
        } else if ("pktLabel".equals(this.printType)) {
            printPktLabel();
            type = WaveHeader.FLAG_PRINT_PKT_LABEL;
        } else if ("inspectionReport".equals(this.printType)) {
            printInspectionReport();
            type = WaveHeader.FLAG_PRINT_INSPECTION_REPORT;
        } else if ("yaoTo".equals(this.printType)) {
            printYaoTo();
            type = WaveHeader.FLAG_PRINT_YAO_TO;
        }


        changeWavePrintType(type);
        if ("pick_waybill".equals(this.printType)) {
            printPickWaybill();
        } else if ("pick_do_waybill".equals(this.printType)) {
            printPickDoWaybill();
        }
        WmsUtil.clearSession();

        this.getSelectedMap().clear();
        this.query();
    }

    /**
     * 消费"打印购物清单"标记
     * 当打印出库单时，将波次的doNeedPrint标记设置为0（已消费）
     */
    private void consumeShoppingListPrintFlag() {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (!ids.isEmpty()) {
            // 更新波次的doNeedPrint标记为0（已消费）
            waveService.updateDoNeedPrintFlag(ids, 0);
            log.info("消费打印购物清单标记，波次IDs: {}", ids);
        }
    }

    /**
     * 根据选定波次id批量更新波次是否打印标志位为已打印
     */
    private void changeWavePrintType(Integer type) {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        waveService.updateWavePrintFlag(ids, type);
    }

    /**
     * 打印发货单信息
     */
    private void printDo() {
        this.printData = "[]";
        printContent = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        WaveHeader waveHeader = waveService.getWave(ids.get(0));
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }

        if (StringUtil.isIn(waveHeader.getWaveType(), Constants.WaveType.WAVE_NORMAL.getValue(),
                Constants.WaveType.WAVE_2B.getValue())) {
            printContent = printDoService.genDataByWave(ids);
        } else {
            List<String> pages = printService.getDoReportByWaveIds(ids);

            this.doPrintCfg = printDoService.setDoPrintCfg(ids.get(0), null);
            this.printData = new JSONArray(pages).toString();
        }
    }
    private void printPickDoWaybill() {
        this.printData = "[]";
        this.waybillPrintData="[]";
        this.printContent = "";
        this.printContent1 = "";
        this.printContent2 = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<WaveHeader> waveHeaders = waveService.getWaveList(ids);
        Integer autoWaveType = null;
        for (WaveHeader waveHeader1 : waveHeaders) {
            Integer tempType = waveHeader1.getAutoType() != null
                    && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader1.getAutoType())
                    ? YesNo.YES.getValue()
                    : YesNo.NO.getValue();
            if (autoWaveType == null) {
                autoWaveType = tempType;
            } else if (!autoWaveType.equals(tempType)) {
                throw new DeliveryException(DeliveryException.WAVE_IS_MIX);
            }
        }
        WaveHeader waveHeader = waveService.getWave(ids.get(0));
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        // ==生成面单打印数据
        for (Long waveId : ids) {
            if (tempCartonService.existEmptyCartonNo(waveId)) {
                throw new DeliveryException(DeliveryException.PRINT_TEMPCARTON_NOEXIST);
            }
        }
        List<PrintData> resultList = printCartonDispatcher
                .printTempCarton(deliveryOrderService.getIdListByWaveIds(ids));

        printContent = WaybillPrintHelper.getPrintJs(resultList);
        this.waybillPrintData=new Gson().toJson(resultList);
        // 生成订单打印信息
        if (StringUtil.isIn(waveHeader.getWaveType(), Constants.WaveType.WAVE_NORMAL.getValue(),
                Constants.WaveType.WAVE_2B.getValue())) {
            printContent1 = printDoService.genDataByWave(ids);
        } else {
            List<String> pages = printService.getDoReportByWaveIds(ids);

            this.doPrintCfg = printDoService.setDoPrintCfg(ids.get(0), null);
            this.printData = new JSONArray(pages).toString();
        }
        // == 生成拣货单打印信息
        PickHeaderFilter pktFilter = new PickHeaderFilter();
        pktFilter.setWaveIds(ids);
        // 读取指定区域不打印的设置。
        List<String> notPrintRegions = Config.getByDelimit(Keys.Delivery.pick_notPrintRegions, ",",
                Config.ConfigLevel.WAREHOUSE);
        if (ListUtil.isNotEmpty(notPrintRegions)) {
            List<Long> notPrintRegionLs = ListUtil.convert(notPrintRegions, new ListUtil.Convertor<String, Long>() {
                @Override
                public Long convert(String id) {
                    return Long.valueOf(id);
                }
            });
            pktFilter.setRegionIdsNotIn(notPrintRegionLs);
        }

        List<PickHeader> pkts = pickHeaderService.query(pktFilter);
        this.doPrintCfg = new PrintCfg();
        printContent2 = printService.printPickByPkt(pkts, YesNo.YES.getValue().equals(autoWaveType));
//        printData = new Gson().toJson(resultList);
        waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_PICK_DO_WAYBILL);

    }
    private void printPickWaybill() {
        this.waybillPrintData = "[]";
        this.printContent = "";
        this.printContent1 = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        WaveHeader waveHeader = waveService.getWave(ids.get(0));
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        List<WaveHeader> waveHeaders = waveService.getWaveList(ids);
        Integer autoWaveType = null;
        for (WaveHeader waveHeader1 : waveHeaders) {
            Integer tempType = waveHeader1.getAutoType() != null
                    && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader1.getAutoType())
                    ? YesNo.YES.getValue()
                    : YesNo.NO.getValue();
            if (autoWaveType == null) {
                autoWaveType = tempType;
            } else if (!autoWaveType.equals(tempType)) {
                throw new DeliveryException(DeliveryException.WAVE_IS_MIX);
            }
        }
        // == 生成拣货单打印信息
        PickHeaderFilter pktFilter = new PickHeaderFilter();
        pktFilter.setWaveIds(ids);
        // 读取指定区域不打印的设置。
        List<String> notPrintRegions = Config.getByDelimit(Keys.Delivery.pick_notPrintRegions, ",",
                Config.ConfigLevel.WAREHOUSE);
        if (ListUtil.isNotEmpty(notPrintRegions)) {
            List<Long> notPrintRegionLs = ListUtil.convert(notPrintRegions, new ListUtil.Convertor<String, Long>() {
                @Override
                public Long convert(String id) {
                    return Long.valueOf(id);
                }
            });
            pktFilter.setRegionIdsNotIn(notPrintRegionLs);
        }

        List<PickHeader> pkts = pickHeaderService.query(pktFilter);

        this.doPrintCfg = new PrintCfg();

        printContent = printService.printPickByPkt(pkts, YesNo.YES.getValue().equals(autoWaveType));

        // ==生成面单打印数据
        // 验证波次中是否存在下单失败的
        for (Long waveId : ids) {
            if (tempCartonService.existEmptyCartonNo(waveId)) {
                throw new DeliveryException(DeliveryException.PRINT_TEMPCARTON_NOEXIST);
            }
        }
        List<PrintData> resultList = printCartonDispatcher
                .printTempCarton(deliveryOrderService.getIdListByWaveIds(ids));
        waybillPrintData = new Gson().toJson(resultList);
        printContent1= WaybillPrintHelper.getPrintJs(resultList);
        waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_PICK_WAYBILL);

    }

    public void detailPrintPktLabel() {
        this.getSelectedMap().put(this.getWaveId(), true);
        this.printPktLabel();
    }

    /**
     * 打印所选波次的拣货标签
     */
    public void printPktLabel() {
        this.printContent = "";
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        printContent = printPickLabelService.printPickLabel(ids);
    }

    /**
     * 打印所选波次的质检报告
     */
    public void printInspectionReport() {
        printContent = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        WaveHeader waveHeader = waveService.getWave(ids.get(0));
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        printContent = printService.genPrintInspectionByWave(ids);

    }

    /**
     * 打印所选波次的随货同行单
     */
    public void printYaoTo() {
        printContent = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        if (ListUtil.isNullOrEmpty(ids)) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        WaveHeader waveHeader = waveService.getWave(ids.get(0));
        if (waveHeader == null) {
            throw new DeliveryException(DeliveryException.PRINT_DATA_IS_EMPTY);
        }
        printContent = printService.genPrintYaoToByWave(ids);

    }

    /**
     * 导出明细信息
     * 
     * @throws IOException
     */
    public void detailExport() throws IOException {
        this.getSelectedMap().put(this.getWaveId(), true);
        this.export();
    }

    /**
     * 根据所选类型，导出Excel
     * 
     * @throws IOException
     */
    public void export() throws IOException {
        this.printData = "[]";

        // TODO 常量可优化
        if ("stt".equals(this.printType)) {
            exportStt();
        } else if ("pick".equals(this.printType)) {
            throw new UnsupportedOperationException();
        } else if ("do".equals(this.printType)) {
            exportDo();
        }
    }

    /**
     * 导出分拣单
     * 
     * @throws IOException
     */
    public void exportStt() throws IOException {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        List<WaveHeader> waves = waveService.getWaveList(ids);
        byte[] exportData = printService.exportSttReport(waves);
        DownloadUtil.writeToResponse(exportData, DownloadUtil.PDF,
                "wave_stt_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 导出发货单
     * 
     * @throws IOException
     */
    public void exportDo() throws IOException {
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        DoHeaderFilter doHeaderFilter = new DoHeaderFilter();
        doHeaderFilter.setWaveIds(ids);
        List<DeliveryOrderHeader> doHeaderList = deliveryOrderService.query(doHeaderFilter);
        byte[] exportData = printService.exportDoReportByHeaderList(doHeaderList);
        DownloadUtil.writeToResponse(exportData, DownloadUtil.PDF,
                "do_" + DateUtil.dateToString(new Date(), "yyMMddHHmmss") + ".pdf");
    }

    /**
     * 打印拣货单
     */
    private void printPick() {
        this.printData = "[]";
        printContent = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        PickHeaderFilter pktFilter = new PickHeaderFilter();
        pktFilter.setWaveIds(ids);
        // 读取指定区域不打印的设置。
        List<String> notPrintRegions = Config.getByDelimit(Keys.Delivery.pick_notPrintRegions, ",",
                Config.ConfigLevel.WAREHOUSE);
        if (ListUtil.isNotEmpty(notPrintRegions)) {
            List<Long> notPrintRegionLs = ListUtil.convert(notPrintRegions, new ListUtil.Convertor<String, Long>() {
                @Override
                public Long convert(String id) {
                    return Long.valueOf(id);
                }
            });
            pktFilter.setRegionIdsNotIn(notPrintRegionLs);
        }

        List<PickHeader> pkts = pickHeaderService.query(pktFilter);

        List<WaveHeader> waveHeaders = waveService.getWaveList(ids);
        Integer autoWaveType = null;
        for (WaveHeader waveHeader : waveHeaders) {
            Integer tempType = waveHeader.getAutoType() != null
                    && Constants.AutoWaveType.BATCH_GROUP.getValue().equals(waveHeader.getAutoType())
                            ? YesNo.YES.getValue()
                            : YesNo.NO.getValue();
            if (autoWaveType == null) {
                autoWaveType = tempType;
            } else if (!autoWaveType.equals(tempType)) {
                throw new DeliveryException(DeliveryException.WAVE_IS_MIX);
            }
        }
        waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_PICK);

        this.doPrintCfg = new PrintCfg();

        printContent = printService.printPickByPkt(pkts, YesNo.YES.getValue().equals(autoWaveType));
    }

    public void uploadInvoice() {
        List<Long> ids = new ArrayList<Long>();
        List<WaveHeader> initialWaveHeaders = new ArrayList<WaveHeader>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        if (ListUtil.isNotEmpty(ids)) {
            // 验证波次中是否存在下单失败的
            for (Long waveId : ids) {
                WaveHeader waveHeader = waveService.getWave(waveId);
                if (waveHeader != null
                        && Constants.InvoicePrintFlg.INIT.getValue().equals(waveHeader.getInvoicePrintFlag())) {
                    initialWaveHeaders.add(waveHeader);
                }
            }
        } else {
            initialWaveHeaders = waveService
                    .findWaveByPrintFlg(Constants.InvoicePrintFlg.INIT.getValue());
        }
        // 事物在for 循环级别。
        for (WaveHeader initialWaveHeader : initialWaveHeaders) {
            try {
                invoiceJinrenService.writeJinrenInvoice(initialWaveHeader);
            } catch (Exception e) {
                log.error("WriteJinrenInvoiceWriteJob encounter exception: waveNo:" + initialWaveHeader.getWaveNo(), e);
            }
        }
    }

    private void printCarton() {
        this.printData = "[]";
        this.waybillPrintData="[]";
        printContent = "";
        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }
        // 验证波次中是否存在下单失败的
        for (Long waveId : ids) {
            if (tempCartonService.existEmptyCartonNo(waveId)) {
                throw new DeliveryException(DeliveryException.PRINT_TEMPCARTON_NOEXIST);
            }
        }
        List<PrintData> resultList = printCartonDispatcher
                .printTempCarton(deliveryOrderService.getIdListByWaveIds(ids));
        waybillPrintData = new Gson().toJson(resultList);
        printContent = WaybillPrintHelper.getPrintJs(resultList);
        waveService.updateWavePrintFlag(ids, WaveHeader.FLAG_PRINT_CARTON);
    }

    /**
     * 根据所选波次，打印其RTV发货单信息
     */
    public void printRtv() {
        this.printData = "[]";
        printContent = "";

        List<Long> ids = new ArrayList<Long>();
        for (Object id : getSelectedRowList()) {
            ids.add((Long) id);
        }

        List<String> pages = printService.getRtvReportByWaveIds(ids);

        this.printData = new JSONArray(pages).toString();
    }

    public String showPrintFlag(WaveHeader waveHeader) {
        String flagStr = "";
        String fStr = "<i class=\"fa fa-star-o\" aria-hidden=\"true\"></i>";
        String tStr = "<i class=\"fa fa-star\" aria-hidden=\"true\"></i>";
        int printFlag = waveHeader.getPrintFlag().intValue();
        if (FlagUtil.has(printFlag, WaveHeader.FLAG_PRINT_DO)) {
            flagStr += tStr;
        } else {
            flagStr += fStr;
        }
        if (FlagUtil.has(printFlag, WaveHeader.FLAG_PRINT_PICK)) {
            flagStr += tStr;
        } else {
            flagStr += fStr;
        }
        if (FlagUtil.has(printFlag, WaveHeader.FLAG_PRINT_PKT_LABEL)) {
            flagStr += tStr;
        } else {
            flagStr += fStr;
        }
        if (FlagUtil.has(printFlag, WaveHeader.FLAG_PRINT_CARTON)) {
            flagStr += tStr;
        } else {
            flagStr += fStr;
        }
        return flagStr;
    }

    /**
     * 转换渠道编码为渠道名称
     * 参考DeliveryOrderServiceImpl.query方法的逻辑
     */
    private void convertChannelCodeToName(List<WaveDetailDTO> waveDetails) {
        try {
            Map<String, String> channelMap = businessCenterComponent.getChannelMap();

            for (WaveDetailDTO waveDetail : waveDetails) {
                if (waveDetail.getChannelCode() != null) {
                    String channelName = channelMap.getOrDefault(
                        waveDetail.getChannelCode(),
                        waveDetail.getChannelCode()
                    );
                    waveDetail.setChannelName(channelName);
                }
            }
        } catch (Exception e) {
            log.error("转换渠道名称失败", e);
            // 如果转换失败，使用渠道编码作为显示名称
            for (WaveDetailDTO waveDetail : waveDetails) {
                if (waveDetail.getChannelCode() != null) {
                    waveDetail.setChannelName(waveDetail.getChannelCode());
                }
            }
        }
    }

    /**
     * 获取波次中的渠道信息统计
     * @param waveHeader 波次头信息
     * @return 渠道信息字符串，如"ABM(3), VTN(2), OTHER(1)"
     */
    public String getChannelInfo(WaveHeader waveHeader) {
        if (waveHeader == null || waveHeader.getId() == null) {
            return "";
        }

        try {
            List<WaveDetailDTO> waveDetails = deliveryOrderService.qureyWaveDetail(waveHeader.getId());
            if (waveDetails == null || waveDetails.isEmpty()) {
                return "";
            }

            // 转换渠道编码为渠道名称
            convertChannelCodeToName(waveDetails);
            return waveDetails.get(0).getChannelName();
        } catch (Exception e) {
            log.error("获取波次渠道信息失败: waveId={}", waveHeader.getId(), e);
            return "";
        }
    }

    public WaveHeader getWave() {
        return wave;
    }

    public void setWave(WaveHeader wave) {
        this.wave = wave;
    }

    public Long getWaveId() {
        return waveId;
    }

    public void setWaveId(Long waveId) {
        this.waveId = waveId;
    }

    public boolean isInitialized() {
        return initialized;
    }

    public void setInitialized(boolean initialized) {
        this.initialized = initialized;
    }

    public WaveService getWaveService() {
        return waveService;
    }

    public void setWaveService(WaveService waveService) {
        this.waveService = waveService;
    }

    public WaveHeaderFilter getWaveHeaderFilter() {
        return waveHeaderFilter;
    }

    public void setWaveHeaderFilter(WaveHeaderFilter waveHeaderFilter) {
        this.waveHeaderFilter = waveHeaderFilter;
    }

    public Long getRemoveDoId() {
        return removeDoId;
    }

    public void setRemoveDoId(Long removeDoId) {
        this.removeDoId = removeDoId;
    }

    public List<WaveDetailDTO> getDoHeaders() {
        return doHeaders;
    }

    public void setDoHeaders(List<WaveDetailDTO> doHeaders) {
        this.doHeaders = doHeaders;
    }

    public void setPrintType(String printType) {
        this.printType = printType;
    }

    public String getPrintType() {
        return printType;
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    public String getPrintData() {
        return printData;
    }

    public int getDoSize() {
        return doSize;
    }

    public void setDoSize(int doSize) {
        this.doSize = doSize;
    }

    public PrintCfg getDoPrintCfg() {
        return doPrintCfg;
    }

    public void setDoPrintCfg(PrintCfg doPrintCfg) {
        this.doPrintCfg = doPrintCfg;
    }

    public Integer getIsPrintPickTaskByA5() {
        return isPrintPickTaskByA5;
    }

    public void setIsPrintPickTaskByA5(Integer isPrintPickTaskByA5) {
        this.isPrintPickTaskByA5 = isPrintPickTaskByA5;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public Boolean getPrintFlag() {
        return printFlag;
    }

    public void setPrintFlag(Boolean printFlag) {
        this.printFlag = printFlag;
    }

    public Integer getCartonPrintPoint() {
        return cartonPrintPoint;
    }

    public void setCartonPrintPoint(Integer cartonPrintPoint) {
        this.cartonPrintPoint = cartonPrintPoint;
    }

    public String getPrintContent1() {
        return printContent1;
    }

    public void setPrintContent1(String printContent1) {
        this.printContent1 = printContent1;
    }

    public String getPrintContent2() {
        return printContent2;
    }

    public void setPrintContent2(String printContent2) {
        this.printContent2 = printContent2;
    }

    public String getWaybillPrintData() {
        return waybillPrintData;
    }

    public void setWaybillPrintData(String waybillPrintData) {
        this.waybillPrintData = waybillPrintData;
    }
}