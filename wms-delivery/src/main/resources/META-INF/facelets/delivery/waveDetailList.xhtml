<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<ui:composition
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:c="http://java.sun.com/jstl/core"
        xmlns:s="http://jboss.com/products/seam/taglib"
        xmlns:a4j="http://richfaces.org/a4j"
        xmlns:rich="http://richfaces.org/rich"
        xmlns:fw="http://fw.daxia.com/ui"
        template="/templates/dialog.xhtml"
>
    <ui:define name="title">
        <h:outputText value="波次详细信息" />
    </ui:define>
    <ui:define name="body">

        <input type="hidden" id="isLodopPrint" value="1" />
        <input type="hidden" id="isPrintPickTaskByA5" value="#{waveAction.isPrintPickTaskByA5}" />
        <div class="localCla">
            <ui:include src="/templates/include/navigation.xhtml">
                <ui:param name="level_1_menu" value="出库管理" />
                <ui:param name="level_2_menu" value="波次详细信息" />
                <ui:param name="searchPanelId" value="searchPanel" />
            </ui:include>
        </div>
        <h:form id="saveForm">
            <s:div style="align: right" id="operDiv">
                <a4j:commandButton
                        value="#{messages['button.print']}"
                        onclick="showNewPrintTypeDetail(0);return false;"
                        styleClass="btnCla"
                        rendered="#{identity.hasPermission('delivery.wave.detail.print')}"
                />
                <a4j:commandButton
                        value="#{messages['button.preview']}"
                        onclick="showNewPrintTypeDetail(1);return false;"
                        styleClass="btnCla"
                        rendered="#{identity.hasPermission('delivery.wave.detail.edit')}"
                />
                <!--<a4j:commandButton value="#{messages['button.export']}"-->
                <!--onclick="showPrintType(2);return false;" styleClass="btnCla"-->
                <!--rendered="#{identity.hasPermission('delivery.wave.detail.export')}" />-->
                <a4j:commandButton
                        value="打印拣货标签"
                        onclick="printPktLabel();return false;"
                        styleClass="btnCla"
                        rendered="#{identity.hasPermission('delivery.wave.detail.printPktLabel')}"
                />
                <a4j:commandButton
                        onclick="window.close();"
                        value="#{messages['button.close']}"
                        styleClass="btnCla"
                />
                <input type="hidden" id="opType" value="0" />
            </s:div>
            <s:div id="headDataDiv" styleClass="tableContainer">
                <table
                        width="90%"
                        styleClass="tableContainer"
                        rowClasses="listTableSingular,listTableDual"
                >
                    <tr>
                        <td colspan="8" style="font-weight: bold">
                            <h:outputText value="波次信息:" />
                        </td>
                    </tr>
                    <tr>
                        <td colspan="8"><rich:spacer width="100%" height="2"></rich:spacer></td>
                    </tr>
                    <tr>
                        <td align="right"><h:outputText value="波次编号:" /></td>
                        <td>
                            <h:outputText
                                    value="#{waveAction.wave.waveNo}"
                                    rendered="#{!identity.hasPermission('delivery.wave.detail.add')}"
                            />
                            <a4j:commandLink
                                    title="查看拣货单"
                                    rendered="#{identity.hasPermission('delivery.wave.detail.add')}"
                                    value="#{waveAction.wave.waveNo}"
                                    onclick="return daxia.layout.showModelDialog('pktHeaderList.jsf?waveId=#{waveAction.wave.id}', 1000, 600);"
                            />
                        </td>
                        <td align="right"><h:outputText value="波次状态:" /></td>
                        <td>
                            <h:outputText value="#{waveAction.wave.waveStatus}">
                                <fw:dict dictionary="WAVE_STATUS" />
                            </h:outputText>
                        </td>
                        <td align="right"><h:outputText value="波次类型:" /></td>
                        <td>
                            <h:outputText value="#{waveAction.wave.waveType}">
                                <fw:dict dictionary="WAVE_TYPE" />
                            </h:outputText>
                        </td>
                        <td align="right"><h:outputText value="发货单数量:" /></td>
                        <td><h:outputText value="#{waveAction.doHeaders.size()}" /></td>
                    </tr>
                    <tr>
                        <td align="right"><h:outputText value="拣货单数量:" /></td>
                        <td><h:outputText value="#{waveAction.wave.pktCount}" /></td>
                        <td align="right"><h:outputText value="波次计划创建时间:" /></td>
                        <td>
                            <h:outputText value="#{waveAction.wave.createdAt}">
                                <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                            </h:outputText>
                        </td>
                        <td align="right"><h:outputText value="创建人:" /></td>
                        <td><h:outputText value="#{waveAction.wave.createdBy}" /></td>
                        <td align="right"><h:outputText value="打印标识:" /></td>
                        <td style="    width: 45px;">
                            <h:outputText
                                    value="#{waveAction.showPrintFlag(waveAction.wave)}"
                                    escape="false"
                                    styleClass="print_flag"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td align="right"><h:outputText value="备注:" /></td>
                        <td colspan="3"><h:outputText value="#{waveAction.wave.notes}" /></td>
                        <td align="right"><h:outputText value="细分类型:" /></td>
                        <td colspan="3">
                            <h:outputText value="#{waveAction.wave.autoType}" id="waveAutoType">
                                <fw:dict dictionary="WAVE_TYPE_SUBSECTION" />
                            </h:outputText>
                            <!-- 隐藏字段存储波次标识信息 -->
                            <h:inputHidden id="waveEmergencyFlag" value="#{waveAction.wave.emergencyFlag}" />
                            <h:inputHidden id="waveDoNeedPrint" value="#{waveAction.wave.doNeedPrint}" />
                        </td>
                    </tr>
                </table>
            </s:div>
            <s:div
                    id="dataList"
                    style="padding-top: 1px"
                    styleClass="searchText_first,searchInput,searchText,searchInput,searchText,searchInput"
            >
                <rich:messages showDetail="true" showSummary="false" styleClass="message" />
                <h:inputHidden id="doSize" value="#{waveAction.doSize}" />
                <script type="text/javascript">
                    //如果波次中没有DO 则关闭此窗口  目的:撤销完所有的DO 自动关闭此窗口
                    var doSize = $('saveForm:doSize').value;
                    if (doSize == 0) {
                        window.close();
                    }
                </script>
                <rich:panel>
                    <f:facet name="header">
                        <h:outputText value="明细信息" />
                    </f:facet>
                    <rich:dataTable
                            value="#{waveAction.doHeaders}"
                            rowClasses="listTableSingular,listTableDual"
                            var="item"
                            width="100%"
                            rowKeyVar="row"
                    >
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="操作" />
                            </f:facet>
                            <a4j:commandLink
                                    onclick="del(#{item.doId});return false;"
                                    rendered="#{identity.hasPermission('delivery.wave.detail.remove')
                                           and ((item.releaseStatus == 'RL')
                                           and (item.doStatus == '40' or item.doStatus == '00'))}"
                                    reRender="dataList"
                            >
                                <i class="fa fa-remove fa-fw" />
                                <h:outputText value="#{messages['button.delete']}" />
                            </a4j:commandLink>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="行号" />
                            </f:facet>
                            <h:outputText value="#{row + 1}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="分拣格号" />
                            </f:facet>
                            <h:outputText value="#{item.sortGridNo}" />
                        </rich:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="发货单号" />
                            </f:facet>
                            <h:outputText
                                    value="#{item.doNo}"
                                    rendered="#{!identity.hasPermission('delivery.wave.detail.edit')}"
                            />
                            <a4j:commandLink
                                    rendered="#{identity.hasPermission('delivery.wave.detail.edit')}"
                                    value="#{item.doNo}"
                                    onclick="return daxia.layout.showModelDialog('deliveryOrderDetailListView.jsf?doHeaderId=#{item.doId}', 1200, 800);"
                            />
                        </h:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="发货单类型" />
                            </f:facet>
                            <h:outputText value="#{item.doType}">
                                <fw:dict dictionary="ODO_TYPE" />
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="渠道信息" />
                            </f:facet>
                            <h:outputText value="#{item.channelName}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="发货单状态" />
                            </f:facet>
                            <h:outputText value="#{item.doStatus}">
                                <fw:dict dictionary="DO_STATUS" />
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="发货单创建时间" />
                            </f:facet>
                            <h:outputText value="#{item.doCreateTime}">
                                <s:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" />
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="备注" />
                            </f:facet>
                            <h:outputText value="#{item.notes}" />
                        </rich:column>
                    </rich:dataTable>
                </rich:panel>
            </s:div>
            <!--删除do-->
            <a4j:jsFunction name="removeDo" action="#{waveAction.removeDo()}" reRender="dataList">
                <a4j:actionparam name="removeDoId" assignTo="#{waveAction.removeDoId}" />
            </a4j:jsFunction>
            <a4j:htmlCommandLink
                    id="hiddenExportLink"
                    style="display: none"
                    value="hiddenExportLink"
                    action="#{waveAction.detailExport()}"
            />
            <a4j:jsFunction
                    name="doPrint"
                    action="#{waveAction.detailPrint()}"
                    requestDelay="1"
                    oncomplete="doLodopPrint()"
                    reRender="printDataDiv,printDoCfgDiv"
                    onerror="Richfaces.hideModalPanel('printTypePanel');"
            />
            <a4j:jsFunction
                    name="printPktLabel"
                    action="#{waveAction.detailPrintPktLabel()}"
                    requestDelay="1"
                    oncomplete="doLodopPrintPktLabel()"
                    reRender="printDataDiv"
            />
            <h:inputHidden id="printTypeHidden" value="#{waveAction.printType}" />
            <script
                    src="${facesContext.externalContext.requestContextPath}/js/lodopTemplate.js?v=${appConfig.getBuildVersion()}"
            ></script>
            <script
                    src="${facesContext.externalContext.requestContextPath}/res/js/CaiNiaoPrintFuncs.js?v=${appConfig.getBuildVersion()}"
            ></script>
            <script
                    src="${facesContext.externalContext.requestContextPath}/res/print_template/pickTask.js?v=2"
            ></script>
            <script language="javascript">
                //<![CDATA[
                function del(id) {
                    if (
                        window.confirm(
                            '是否将该发货单从波次中撤销？/Do you want to cancel this delivery order from the wave?',
                        )
                    ) {
                        removeDo(id);
                    }
                }
                function isEmpty(str) {
                    if (str == null || str.length == 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
                //]]>
            </script>
            <s:div id="printDataDiv" style="height: 0px; overflow-y: hidden">
                <script>
                    var printData = <h:outputText value="#{waveAction.printData}" escape="false" />;
                    var waybillPrintDataArr= <h:outputText value="#{waveAction.waybillPrintData}" escape="false" />;
                    var pageType = jQuery('#pageTypeHidden').val();
                </script>
                <h:outputText value="#{waveAction.printContent}" escape="false"/>
                <h:outputText value="#{waveAction.printContent1}" escape="false"/>
                <h:outputText value="#{waveAction.printContent2}" escape="false"/>
            </s:div>
            <script>
                var lodopWaybillTemplate = new LodopTemplate({
                    templateName: '电子标签',
                });
                var reportName = '波次单据';
                var lodopPrint = new LodopPrint(
                    {
                        reportName: reportName,
                    },
                    printData,
                );

                //<![CDATA[
                function doLodopPrint() {
                    debugger
                    if ($('printForm:newPrintType').value == 'pick') {
                        doPrintPick($('opType').value == '0');
                    } else if ($('printForm:newPrintType').value == 'pick_do_waybill') {
                        // 拣货单+出库单+物流面单组合打印
                        try {
                            doPrintPick($('opType').value == '0');
                            if (jQuery('#printStyle').size() > 0) {
                                printDoByTem($('opType').value == '0');
                            }
                            var size = waybillPrintDataArr.size();
                            if (size == 0) {
                                alert('打印数据为空/Print data is empty');
                                return false;
                            }
                            var isPrint = $('opType').value == '0';
                            for (var i = 0; i < size; i++) {
                                wayBillPrintData = waybillPrintDataArr[i];
                                //优先找配送商打印机，再找面单打印机
                                var carrierPrinter = window.localStorage.getItem(
                                    'printer_face_' + wayBillPrintData.carrierId,
                                );
                                var cartonPrinter =
                                    carrierPrinter == null
                                        ? window.localStorage.getItem(
                                            'printer_face_' + wayBillPrintData.carrierId,
                                        )
                                        : carrierPrinter;
                                if (wayBillPrintData.printCfg.lodopTemplate != 'wayBillCanniao') {
                                    //暂时通过lodopTemplate来判断是否为菜鸟的电子面单打印
                                    try {
                                        eval(
                                            '' +
                                            wayBillPrintData.printCfg.lodopTemplate +
                                            '(isPrint,' +
                                            wayBillPrintData.data +
                                            ',cartonPrinter)',
                                        );
                                    } catch (e) {
                                        alert(e);
                                    }
                                } else {
                                    printCaiNiaoWayBill(wayBillPrintData.data, !isPrint, cartonPrinter);
                                }
                            }
                            printData = [];
                        } catch (e) {
                            console.log(e);
                        }
                    } else if ($('printForm:newPrintType').value == 'pick_waybill') {
                        // 拣货单+物流面单组合打印
                        try {
                            // 打印拣货单
                            doPrintPick($('opType').value == '0');

                            var size = waybillPrintDataArr.size();
                            if (size == 0) {
                                alert('打印数据为空/Print data is empty');
                                return false;
                            }
                            var isPrint = $('opType').value == '0';
                            for (var i = 0; i < size; i++) {
                                wayBillPrintData = waybillPrintDataArr[i];
                                //优先找配送商打印机，再找面单打印机
                                var carrierPrinter = window.localStorage.getItem(
                                    'printer_face_' + wayBillPrintData.carrierId,
                                );
                                var cartonPrinter =
                                    carrierPrinter == null
                                        ? window.localStorage.getItem(
                                            'printer_face_' + wayBillPrintData.carrierId,
                                        )
                                        : carrierPrinter;
                                if (wayBillPrintData.printCfg.lodopTemplate != 'wayBillCanniao') {
                                    //暂时通过lodopTemplate来判断是否为菜鸟的电子面单打印
                                    try {
                                        eval(
                                            '' +
                                            wayBillPrintData.printCfg.lodopTemplate +
                                            '(isPrint,' +
                                            wayBillPrintData.data +
                                            ',cartonPrinter)',
                                        );
                                    } catch (e) {
                                        alert(e);
                                    }
                                } else {
                                    printCaiNiaoWayBill(wayBillPrintData.data, !isPrint, cartonPrinter);
                                }
                            }
                            printData = [];
                        } catch (e) {
                            console.log(e);
                        }
                    } else if ($('printForm:newPrintType').value == 'waybill') {
                        var size = waybillPrintDataArr.size();
                        if (size == 0) {
                            alert('打印数据为空/Print data is empty');
                            return false;
                        }
                        var isPrint = $('opType').value == '0';
                        for (var i = 0; i < size; i++) {
                            wayBillPrintData = waybillPrintDataArr[i];
                            //优先找配送商打印机，再找面单打印机
                            var carrierPrinter = window.localStorage.getItem(
                                'printer_face_' + wayBillPrintData.carrierId,
                            );
                            var cartonPrinter =
                                carrierPrinter == null
                                    ? window.localStorage.getItem(
                                        'printer_face_' + wayBillPrintData.carrierId,
                                    )
                                    : carrierPrinter;
                            if (wayBillPrintData.printCfg.lodopTemplate != 'wayBillCanniao') {
                                //暂时通过lodopTemplate来判断是否为菜鸟的电子面单打印
                                try {
                                    eval(
                                        '' +
                                        wayBillPrintData.printCfg.lodopTemplate +
                                        '(isPrint,' +
                                        wayBillPrintData.data +
                                        ',cartonPrinter)',
                                    );
                                } catch (e) {
                                    alert(e);
                                }
                            } else {
                                printCaiNiaoWayBill(wayBillPrintData.data, !isPrint, cartonPrinter);
                            }
                        }
                        printData = [];
                    } else {
                        if (jQuery('#printStyle').size() > 0) {
                            printDoByTem($('opType').value == '0');

                            return;
                        } else {
                            lodopPrint.setPrintData(printData);

                            if (!isEmpty(pageType)) {
                                lodopPrint.setPageType(pageType);
                                lodopPrint.setReplace(function (htmlStr) {
                                    return htmlStr.replace(/font-size: 11px/g, 'font-size:12px');
                                });
                            } else {
                                lodopPrint.setPageType('A4');
                            }
                            if ($('opType').value == '0') lodopPrint.print();
                            else lodopPrint.preview();
                        }
                    }
                }
                // 新的打印弹窗函数 - 详情页
                function showNewPrintTypeDetail(opType) {
                    // 获取当前波次信息
                    var waveInfo = getCurrentWaveInfo();

                    // 确定打印类型配置
                    var printTypeConfig = getWavePrintConfigDetail(waveInfo);

                    // 配置下拉框选项
                    configurePrintTypeOptionsDetail(printTypeConfig);

                    $('opType').value = opType;
                    Richfaces.showModalPanel('newPrintTypePanel');
                }

                // 获取当前波次信息
                function getCurrentWaveInfo() {
                    // 从隐藏字段中获取波次标识信息
                    var emergencyFlag = parseInt($('saveForm\:waveEmergencyFlag').value) || 0; // 组合标记

                    // 从细分类型字段获取波次类型
                    var autoTypeText = $('saveForm\:waveAutoType').innerText;
                    var autoType = autoTypeText || '普通'; // 如果没有获取到，默认为普通

                    // 从隐藏字段中获取打印购物清单标记
                    var doNeedPrint = parseInt($('saveForm\:waveDoNeedPrint').value) || 0; // 打印购物清单标记

                    console.log('获取到的波次信息:', {
                        emergencyFlag: emergencyFlag,
                        autoType: autoType,
                        doNeedPrint: doNeedPrint
                    });

                    return {
                        emergencyFlag: emergencyFlag,
                        autoType: autoType,
                        doNeedPrint: doNeedPrint,
                    };
                }

                // 获取波次的打印配置 - 详情页版本
                function getWavePrintConfigDetail(waveInfo) {
                    var config = {
                        availableOptions: [
                            'pick_waybill',
                            'pick_do_waybill',
                            'pick',
                            'pktLabel',
                            'waybill',
                            'do'
                        ],
                        defaultValue: '',
                        disabledOptions: [],
                    };

                    // 检查渠道信息，如果不是ABM或VTN，默认为pick
                    var hasNonABMVTNChannel = true;
                    if (hasNonABMVTNChannel) {
                        config.disabledOptions = ['pick_do_waybill','pick_waybill','waybill']
                        config.defaultValue = 'pick';
                        return config;
                    }

                    // 根据波次类型和标记配置选项
                    if (waveInfo.emergencyFlag === 1) {
                        // 组合标记
                        // config.disabledOptions = ['pick_do_waybill', 'pick'];
                        config.defaultValue = 'pick_waybill';
                    } else if (waveInfo.autoType === '批量团购' || waveInfo.waveType !='普通波次') {
                        // 提总波次
                        // config.disabledOptions = ['pick_do_waybill', 'pick_waybill'];
                        config.defaultValue = 'pick';
                    } else if (waveInfo.doNeedPrint === 0) {
                        // 普通波次无"打印购物清单"标记
                        // config.disabledOptions = ['pick_do_waybill', 'pick'];
                        config.defaultValue = 'pick_waybill';
                    } else {
                        // 普通波次有"打印购物清单"标记
                        // config.disabledOptions = ['pick_waybill', 'pick'];
                        config.defaultValue = 'pick_do_waybill';
                    }

                    return config;
                }

                // 检查波次中是否有非ABM或VTN的渠道
                function checkForNonABMVTNChannels() {
                    var hasNonABMVTN = false;

                    // 遍历表格中的渠道信息列
                    jQuery('table.rich-table tbody tr').each(function() {
                        var $row = jQuery(this);
                        // 查找渠道信息列（假设是第6列，根据实际情况调整）
                        var $channelCell = $row.find('td').eq(5); // 0-based index，渠道信息列
                        if ($channelCell.length > 0) {
                            var channelName = $channelCell.text().trim();
                            // 检查渠道名称，如果不是ABM或VTN，则返回true
                            if (channelName && channelName.indexOf( 'ABM') <0&& channelName .indexOf( 'VTN')<0 && channelName !== '') {
                                hasNonABMVTN = true;
                                return false; // 跳出循环
                            }
                        }
                    });

                    return hasNonABMVTN;
                }

                // 配置打印类型下拉框选项 - 详情页版本
                function configurePrintTypeOptionsDetail(config) {
                    var selectElement = jQuery('#printForm\\:newPrintType');
                    selectElement.find('option').each(function () {
                        var option = jQuery(this);
                        var value = option.val();

                        if (value === '') return; // 跳过"请选择"选项

                        if (config.disabledOptions.indexOf(value) !== -1) {
                            option.hide().attr('disabled', true);
                        } else {
                            option.show().attr('disabled', false);
                        }
                    });

                    // 设置默认值
                    selectElement.val(config.defaultValue);
                }

                // 验证并提交新打印 - 详情页版本
                function validateAndSubmitNewPrintDetail() {
                    var selectedType = jQuery('#printForm\\:newPrintType').val();
                    if (!selectedType) {
                        alert('请选择单据类型');
                        return false;
                    }

                    Richfaces.hideModalPanel('newPrintTypePanel');

                    // 根据选择的类型执行相应的打印逻辑
                    executeNewPrintLogicDetail(selectedType);
                    return false;
                }

                // 执行新的打印逻辑 - 详情页版本
                function executeNewPrintLogicDetail(printType) {
                    // 根据打印类型设置相应的参数并调用打印方法
                    switch (printType) {
                        case 'pick_waybill':
                            // 拣货单+物流面单
                            $('saveForm:printTypeHidden').value = 'pick_waybill';
                            doPrint();
                            break;
                        case 'pick_do_waybill':
                            // 拣货单+出库单+物流面单
                            $('saveForm:printTypeHidden').value = 'pick_do_waybill';
                            doPrint();
                            break;
                        case 'pick':
                            // 拣货单
                            $('saveForm:printTypeHidden').value = 'pick';
                            doPrint();
                            break;
                        case 'pktLabel':
                            // PDA拣货标签
                            printPktLabel();
                            break;
                        case 'waybill':
                            // 物流面单
                            $('saveForm:printTypeHidden').value = 'carton';
                            doPrint();
                            break;
                        case 'do':
                            // 出库单
                            $('saveForm:printTypeHidden').value = 'do';
                            doPrint();
                            break;
                    }
                }

                // 原有函数保留用于兼容
                function showPrintType(opType) {
                    jQuery('#printForm:printType option').show();
                    jQuery('#printForm:printType option').selected = false;

                    $('opType').value = opType;
                    if (opType == 2) {
                        jQuery('#printForm:printType option')[1].selected = true;
                        jQuery('#printForm:printType option:eq(0)').hide();
                    }

                    Richfaces.showModalPanel('printTypePanel');
                }
                function reportTypeSubmit() {
                    $('saveForm:printTypeHidden').value = $('printForm:printType').value;

                    if ($('opType').value == '0' || $('opType').value == '1') doPrint();
                    else $('saveForm:hiddenExportLink').click();
                }
                //]]>
            </script>
            <link rel="icon" href="/res/favicon.ico" type="image/x-icon" />
            <link rel="stylesheet" type="text/css" href="${facesContext.externalContext.requestContextPath}/res/font-awesome-4.7.0/css/font-awesome.min.css" />
            <!-- 引入tipsy提示插件 -->
            <link
                    rel="stylesheet"
                    type="text/css"
                    href="${facesContext.externalContext.requestContextPath}/res/tipsy/tipsy.css"
            />
            <script
                    type="text/javascript"
                    src="${facesContext.externalContext.requestContextPath}/res/tipsy/jquery.tipsy.js"
            ></script>
            <script>
                // 初始化打印标识提示功能
                jQuery(document).ready(function () {
                    jQuery('span.print_flag')
                        .parent()
                        .tipsy({fallback: '送货单/拣货单/拣货标签/箱标签'});
                });
            </script>
            <script>
                var lodopPrintPkt = new LodopPrint(
                    {
                        reportName: '拣货标签',
                    },
                    printData,
                );
                //<![CDATA[
                function doLodopPrintPktLabel() {
                    doPrintPktLabel(true);
                    //        	lodopPrintPkt.setPrintData(printData);
                    //        	lodopPrintPkt.print();
                }
                //]]>
            </script>
        </h:form>
        <h:form id="printForm">
            <!--新的打印类型弹窗 -->
            <s:div id="newPrintTypeDiv">
                <rich:modalPanel id="newPrintTypePanel" width="400" height="200">
                    <f:facet name="header">
                        <h:outputText value="选择单据类型" />
                    </f:facet>
                    <s:div style="padding: 20px">
                        <table width="100%">
                            <tr>
                                <td><h:outputLabel value="单据类型：" /></td>
                                <td>
                                    <h:selectOneMenu id="newPrintType" style="width: 250px">
                                        <f:selectItem itemLabel="请选择" itemValue="" />
                                        <c:if test="#{waveAction.needPrintCarton()}">
                                            <f:selectItem
                                                    itemLabel="拣货单+物流面单"
                                                    itemValue="pick_waybill"
                                            />
                                            <f:selectItem
                                                    itemLabel="拣货单+出库单+物流面单"
                                                    itemValue="pick_do_waybill"
                                            />
                                        </c:if>
                                        <f:selectItem itemLabel="拣货单" itemValue="pick" />
                                        <f:selectItem
                                                itemLabel="PDA拣货标签"
                                                itemValue="pktLabel"
                                        />
                                        <f:selectItem itemLabel="物流面单" itemValue="waybill" />
                                        <f:selectItem itemLabel="出库单" itemValue="do" />
                                    </h:selectOneMenu>
                                </td>
                            </tr>
                        </table>
                    </s:div>
                    <s:div style="text-align: center; margin-top: 20px">
                        <a4j:commandLink
                                styleClass="btnCla"
                                onclick="return validateAndSubmitNewPrintDetail();"
                        >
                            <i class="fa fa-check fa-fw" />
                            <h:outputText value="确定" />
                        </a4j:commandLink>
                        <a4j:commandLink
                                styleClass="btnCla"
                                onclick="Richfaces.hideModalPanel('newPrintTypePanel');return false;"
                                style="margin-left: 10px"
                        >
                            <i class="fa fa-remove fa-fw" />
                            <h:outputText value="取消" />
                        </a4j:commandLink>
                    </s:div>
                </rich:modalPanel>
            </s:div>

            <!--原打印类型的模式对话框 - 保留用于兼容 -->
            <s:div id="printTypeDiv" style="display: none">
                <rich:modalPanel id="printTypePanel" width="300" height="150">
                    <s:div style="text-align: center; margin-top: 20px">
                        <h:selectOneMenu
                                id="printType"
                                name="printType"
                                value="#{waveAction.printType}"
                        >
                            <!-- <f:selectItem itemLabel="所有单据" itemValue="all" /> -->
                            <f:selectItem itemLabel="出库单" itemValue="do" />
                            <f:selectItem itemLabel="拣货单" itemValue="pick" />
                            <c:if test="#{waveAction.wave.emergencyFlag eq 1}">
                                <f:selectItem itemLabel="面单" itemValue="waybill" />
                            </c:if>
                        </h:selectOneMenu>
                    </s:div>
                    <s:div style="text-align: center; margin-top: 40px">
                        <a4j:commandLink
                                styleClass="btnCla"
                                onclick="reportTypeSubmit();return false;"
                        >
                            <i class="fa fa-check fa-fw" />
                            <h:outputText value="确定" />
                        </a4j:commandLink>
                        <a4j:commandLink
                                styleClass="btnCla"
                                onclick="Richfaces.hideModalPanel('printTypePanel');return false;"
                        >
                            <i class="fa fa-remove fa-fw" />
                            <h:outputText value="取消" />
                        </a4j:commandLink>
                    </s:div>
                </rich:modalPanel>
            </s:div>
        </h:form>
        <s:div id="printDoCfgDiv" style="display: none">
            <h:inputHidden id="pageTypeHidden" value="#{waveAction.doPrintCfg.pageType}" />
        </s:div>
    </ui:define>
</ui:composition>
